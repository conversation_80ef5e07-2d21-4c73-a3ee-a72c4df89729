# HSN UI Kit - 純 Jaspr 現代化元件庫

This project has been successfully configured to use **HSN UI Kit** - a modern component library built with pure Jaspr approach, combining **HSN Design System** with **Tailwind CSS CDN**.

## 🎯 設計理念

### ✅ 純 Jaspr 方式
- **無 Node.js 依賴**: 符合 Jaspr 設計理念
- **純 Dart 生態系統**: 避免 JavaScript 工具鏈複雜性
- **簡化開發流程**: `jaspr create` → `jaspr serve` → `jaspr build`

### ✅ 現代化 UI 體驗
- **HSN 設計系統**: 保留既有的設計一致性
- **Tailwind CDN**: 提供豐富的工具類別
- **語意化元件**: 易於使用和維護的 Dart 元件 API

### 2. 檔案結構

```
nomad_spot_tw/
├── package.json              # Node.js 依賴管理
├── tailwind.config.js        # Tailwind + DaisyUI 配置
├── web/styles/
│   ├── tailwind.css         # 源 CSS 檔案 (包含 HSN 設計系統)
│   ├── output.css           # 編譯後的 CSS 檔案
│   ├── hsn_variables.css    # HSN 設計系統變數
│   ├── hsn_components.css   # HSN 設計系統元件
│   └── hsn_animations.css   # HSN 設計系統動畫
└── lib/components/
    └── daisyui_examples.dart # DaisyUI 元件示例
```

### 3. 已安裝的套件

- **tailwindcss**: 核心 CSS 框架
- **daisyui**: Tailwind 元件庫插件
- **@tailwindcss/typography**: 排版插件
- **@tailwindcss/forms**: 表單樣式插件

## 如何使用 Tailwind CSS + DaisyUI

### 1. DaisyUI 元件使用方式

```dart
// DaisyUI 按鈕
button(classes: 'btn btn-primary', [text('主要按鈕')])
button(classes: 'btn btn-secondary', [text('次要按鈕')])
button(classes: 'btn btn-outline', [text('外框按鈕')])

// DaisyUI 卡片
div(classes: 'card bg-base-100 shadow-xl', [
  div(classes: 'card-body', [
    h2(classes: 'card-title', [text('卡片標題')]),
    p([text('卡片內容...')]),
    div(classes: 'card-actions justify-end', [
      button(classes: 'btn btn-primary', [text('行動按鈕')])
    ])
  ])
])

// DaisyUI 警告框
div(classes: 'alert alert-success', [
  span([text('操作成功！')])
])
```

### 2. 結合 Tailwind 工具類

```dart
// 結合 DaisyUI 元件與 Tailwind 工具類
div(classes: 'card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300', [
  // 卡片內容...
])

// 響應式設計
div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6', [
  // 網格內容...
])
```

### 3. HSN 設計系統整合

```dart
// 使用 HSN 顏色
div(classes: 'bg-nomad-primary text-white p-nomad-lg', [
  text('使用 HSN 主色調')
])

// 使用 HSN 間距
div(classes: 'p-nomad-md m-nomad-sm', [
  text('使用 HSN 間距系統')
])
```

## 開發流程

### 1. 啟動開發服務器

```bash
# 重要：使用 jaspr serve 而不是 dart run lib/main.dart
jaspr serve
```

### 2. CSS 編譯流程

```bash
# 開發模式：監聽檔案變化並自動編譯
npm run build-css

# 或手動編譯一次
npx tailwindcss -i ./web/styles/tailwind.css -o ./web/styles/output.css

# 生產模式：壓縮 CSS
npm run build-css-prod
```

### 3. 修改樣式後的步驟

1. 編輯 `web/styles/tailwind.css` 或 Dart 元件
2. 執行 CSS 編譯 (如果使用 `--watch` 會自動編譯)
3. 重新整理瀏覽器查看變化

### 4. 服務器地址

開發服務器運行在：<http://localhost:8080>

## 可用功能

### Tailwind CSS 功能

- ✅ 所有 Tailwind 工具類 (顏色、間距、排版、Flexbox、Grid 等)
- ✅ 響應式設計類別 (`sm:`, `md:`, `lg:`, `xl:`, `2xl:`)
- ✅ 狀態變體 (`:hover`, `:focus`, `:active`, `:disabled` 等)
- ✅ 暗色模式支援 (`dark:`)

### DaisyUI 元件

- ✅ 按鈕 (`btn`, `btn-primary`, `btn-outline` 等)
- ✅ 卡片 (`card`, `card-body`, `card-title` 等)
- ✅ 表單 (`input`, `textarea`, `select`, `checkbox` 等)
- ✅ 警告框 (`alert`, `alert-success`, `alert-error` 等)
- ✅ 導航列 (`navbar`, `menu`, `dropdown` 等)
- ✅ 模態框 (`modal`, `modal-box` 等)
- ✅ 標籤頁 (`tabs`, `tab`, `tab-content` 等)

### HSN 設計系統整合

- ✅ HSN 顏色變數 (`bg-nomad-primary`, `text-nomad-workspace` 等)
- ✅ HSN 間距系統 (`p-nomad-lg`, `m-nomad-md` 等)
- ✅ HSN 動畫時間 (`duration-nomad-fast` 等)

## 專案範例

### DaisyUI 元件示例

查看 `lib/components/daisyui_examples.dart` 檔案，包含：

- **按鈕範例**: 各種樣式和尺寸的按鈕
- **卡片範例**: 基本卡片、緊湊卡片、主色調卡片
- **表單範例**: 完整的聯絡表單，包含各種輸入元件
- **警告框範例**: 資訊、成功、警告、錯誤提示
- **導航列範例**: 響應式導航列，包含下拉選單

### 使用範例

```dart
// 在任何 Jaspr 元件中使用
import 'package:nomad_spot_tw/components/daisyui_examples.dart';

class MyPage extends StatelessComponent {
  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield DaisyUIExamples();
  }
}
```

## 重要注意事項

### 開發環境

- ⚠️ **必須使用 `jaspr serve`** 而不是 `dart run lib/main.dart`
- ✅ **CSS 編譯**: 修改樣式後需要重新編譯 CSS
- ✅ **熱重載**: `jaspr serve` 支援熱重載功能

### 生產環境

- 🚀 **CSS 優化**: 使用 `npm run build-css-prod` 產生壓縮版本
- 🎯 **未使用樣式清理**: Tailwind 會自動移除未使用的 CSS
- 📦 **檔案大小**: DaisyUI 只會包含實際使用的元件樣式

### 最佳實踐

- 🎨 **優先使用 DaisyUI 元件**: 語意化且一致的設計
- 🔧 **搭配 Tailwind 工具類**: 微調樣式和響應式設計
- 🎭 **整合 HSN 設計系統**: 保持品牌一致性
- 📱 **響應式優先**: 使用 `sm:`, `md:`, `lg:` 等前綴
