/* DaisyUI + HSN 橋接樣式 */
/* 結合 DaisyUI 現代化設計與 HSN 品牌特色 */

/* 增強按鈕樣式 */
.hsn-btn-enhanced {
  @apply transition-all duration-300 ease-in-out;
  @apply hover:scale-105 active:scale-95;
  @apply shadow-md hover:shadow-lg;
}

.hsn-btn-enhanced:hover {
  transform: translateY(-2px);
}

/* 增強卡片樣式 */
.hsn-card-enhanced {
  @apply border border-base-300/50;
  @apply backdrop-blur-sm;
}

.hsn-card-enhanced:hover {
  @apply border-primary/30;
  transform: translateY(-4px);
}

/* 特色卡片漸層效果 */
.hsn-card-enhanced.bg-gradient-to-br {
  background: linear-gradient(135deg, 
    hsl(var(--p)) 0%, 
    hsl(var(--s)) 100%);
}

/* 增強統計卡片 */
.hsn-stats-enhanced {
  @apply border border-base-300/30;
  @apply backdrop-blur-sm;
}

.hsn-stats-enhanced .stat {
  @apply transition-all duration-300;
}

.hsn-stats-enhanced .stat:hover {
  @apply bg-base-200/50;
  transform: scale(1.02);
}

/* 增強導航列 */
.hsn-navbar-enhanced {
  @apply backdrop-blur-md;
  @apply border-b border-base-300/30;
}

.hsn-navbar-enhanced .btn-ghost:hover {
  @apply bg-primary/10 text-primary;
}

/* 增強警告框 */
.hsn-alert-enhanced {
  @apply border-l-4;
  @apply backdrop-blur-sm;
}

.hsn-alert-enhanced.alert-info {
  @apply border-l-info bg-info/5;
}

.hsn-alert-enhanced.alert-success {
  @apply border-l-success bg-success/5;
}

.hsn-alert-enhanced.alert-warning {
  @apply border-l-warning bg-warning/5;
}

.hsn-alert-enhanced.alert-error {
  @apply border-l-error bg-error/5;
}

/* 增強徽章 */
.hsn-badge-enhanced {
  @apply transition-all duration-200;
  @apply hover:scale-110;
  @apply shadow-sm;
}

/* 增強表單控制項 */
.hsn-form-enhanced .input:focus {
  @apply ring-2 ring-primary/20;
  @apply border-primary;
}

.hsn-input-enhanced {
  @apply transition-all duration-200;
}

.hsn-input-enhanced:focus {
  @apply shadow-md;
  transform: translateY(-1px);
}

/* HSN 品牌色彩變數整合 */
:root {
  /* 整合 HSN 色彩到 DaisyUI 主題 */
  --nomad-primary: hsl(var(--p));
  --nomad-secondary: hsl(var(--s));
  --nomad-accent: hsl(var(--a));
  --nomad-tech: hsl(var(--in));
  --nomad-workspace: hsl(var(--su));
  --nomad-interaction: hsl(var(--wa));
}

/* 響應式增強 */
@media (max-width: 768px) {
  .hsn-card-enhanced:hover {
    transform: none; /* 移動裝置上移除懸停效果 */
  }
  
  .hsn-btn-enhanced:hover {
    transform: none;
  }
}

/* 深色主題適配 */
[data-theme="dark"] .hsn-card-enhanced {
  @apply border-base-content/10;
}

[data-theme="dark"] .hsn-navbar-enhanced {
  @apply border-b-base-content/10;
}

/* 動畫關鍵幀 */
@keyframes hsn-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hsn-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 動畫類別 */
.hsn-fade-in {
  animation: hsn-fade-in 0.6s ease-out;
}

.hsn-scale-in {
  animation: hsn-scale-in 0.4s ease-out;
}

/* 漸層背景工具類別 */
.hsn-gradient-primary {
  background: linear-gradient(135deg, 
    hsl(var(--p)) 0%, 
    hsl(var(--s)) 100%);
}

.hsn-gradient-tech {
  background: linear-gradient(135deg, 
    hsl(var(--in)) 0%, 
    hsl(var(--p)) 100%);
}

.hsn-gradient-workspace {
  background: linear-gradient(135deg, 
    hsl(var(--su)) 0%, 
    hsl(var(--a)) 100%);
}

/* 文字漸層效果 */
.hsn-text-gradient {
  background: linear-gradient(135deg, 
    hsl(var(--p)) 0%, 
    hsl(var(--s)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃擬態效果 */
.hsn-glass {
  @apply backdrop-blur-md;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .hsn-glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 工作空間卡片特殊樣式 */
.workspace-card {
  @apply hsn-card-enhanced;
  @apply relative overflow-hidden;
}

.workspace-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, 
    hsl(var(--p)) 0%, 
    hsl(var(--s)) 50%, 
    hsl(var(--a)) 100%);
}

.workspace-card:hover::before {
  height: 6px;
  transition: height 0.3s ease;
}

/* 統計數據動畫 */
.stat-value {
  @apply transition-all duration-500;
}

.stat:hover .stat-value {
  @apply scale-110;
}

/* 按鈕群組樣式 */
.btn-group-hsn .btn {
  @apply hsn-btn-enhanced;
}

.btn-group-hsn .btn:not(:last-child) {
  @apply border-r-0;
}

.btn-group-hsn .btn:first-child {
  @apply rounded-r-none;
}

.btn-group-hsn .btn:last-child {
  @apply rounded-l-none;
}

.btn-group-hsn .btn:not(:first-child):not(:last-child) {
  @apply rounded-none;
}

/* 載入動畫 */
.hsn-loading {
  @apply animate-pulse;
}

.hsn-loading-spinner {
  @apply animate-spin;
}

/* 工具提示增強 */
.tooltip-hsn {
  @apply relative;
}

.tooltip-hsn::after {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2;
  @apply bg-base-content text-base-100 text-sm px-2 py-1 rounded;
  @apply opacity-0 pointer-events-none transition-opacity duration-200;
  content: attr(data-tip);
}

.tooltip-hsn:hover::after {
  @apply opacity-100;
}
