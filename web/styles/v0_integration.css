/* =======================================
 * V0 + DaisyUI + HSN 混合變數系統
 * ======================================= */

/* V0 核心色彩變數 (oklch 色彩空間) */
:root {
  /* V0 基礎色彩系統 */
  --v0-background: oklch(1 0 0);
  --v0-foreground: oklch(0.145 0 0);
  --v0-card: oklch(1 0 0);
  --v0-card-foreground: oklch(0.145 0 0);
  --v0-popover: oklch(1 0 0);
  --v0-popover-foreground: oklch(0.145 0 0);
  --v0-primary: oklch(0.205 0 0);
  --v0-primary-foreground: oklch(0.985 0 0);
  --v0-secondary: oklch(0.97 0 0);
  --v0-secondary-foreground: oklch(0.205 0 0);
  --v0-muted: oklch(0.97 0 0);
  --v0-muted-foreground: oklch(0.556 0 0);
  --v0-accent: oklch(0.97 0 0);
  --v0-accent-foreground: oklch(0.205 0 0);
  --v0-destructive: oklch(0.577 0.245 27.325);
  --v0-destructive-foreground: oklch(0.577 0.245 27.325);
  --v0-border: oklch(0.922 0 0);
  --v0-input: oklch(0.922 0 0);
  --v0-ring: oklch(0.708 0 0);
  --v0-radius: 0.625rem;

  /* V0 圖表色彩 */
  --v0-chart-1: oklch(0.646 0.222 41.116);
  --v0-chart-2: oklch(0.6 0.118 184.704);
  --v0-chart-3: oklch(0.398 0.07 227.392);
  --v0-chart-4: oklch(0.828 0.189 84.429);
  --v0-chart-5: oklch(0.769 0.188 70.08);
}

/* V0 暗色主題 */
.dark {
  --v0-background: oklch(0.145 0 0);
  --v0-foreground: oklch(0.985 0 0);
  --v0-card: oklch(0.145 0 0);
  --v0-card-foreground: oklch(0.985 0 0);
  --v0-popover: oklch(0.145 0 0);
  --v0-popover-foreground: oklch(0.985 0 0);
  --v0-primary: oklch(0.985 0 0);
  --v0-primary-foreground: oklch(0.205 0 0);
  --v0-secondary: oklch(0.269 0 0);
  --v0-secondary-foreground: oklch(0.985 0 0);
  --v0-muted: oklch(0.269 0 0);
  --v0-muted-foreground: oklch(0.708 0 0);
  --v0-accent: oklch(0.269 0 0);
  --v0-accent-foreground: oklch(0.985 0 0);
  --v0-destructive: oklch(0.396 0.141 25.723);
  --v0-destructive-foreground: oklch(0.637 0.237 25.331);
  --v0-border: oklch(0.269 0 0);
  --v0-input: oklch(0.269 0 0);
  --v0-ring: oklch(0.439 0 0);
}

/* V0 + HSN 混合映射 */
:root {
  /* 將 V0 色彩映射到 HSN 語意 */
  --nomad-primary-v0: #015899;           /* 保持遊牧主色 */
  --nomad-tech-v0: #3b82f6;              /* 科技藍 */
  --nomad-workspace-v0: #f59e0b;         /* 工作琥珀 */
  
  /* V0 風格的圓角系統 */
  --v0-radius-sm: calc(var(--v0-radius) - 4px);
  --v0-radius-md: calc(var(--v0-radius) - 2px);
  --v0-radius-lg: var(--v0-radius);
  --v0-radius-xl: calc(var(--v0-radius) + 4px);
}

/* V0 風格的卡片樣式 - 精確還原 */
.v0-card {
  background-color: white;
  color: #0f172a;  /* V0 的深色文字 */
  border: 1px solid #e2e8f0;  /* V0 的淺灰邊框 */
  border-radius: 0.75rem;  /* V0 的圓角大小 */
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);  /* V0 的基礎陰影 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);  /* V0 的過渡曲線 */
  overflow: hidden;  /* V0 的內容裁切 */
}

.v0-card:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);  /* V0 的懸停陰影 */
  transform: translateY(-2px);  /* V0 的懸停上升 */
  border-color: #cbd5e1;  /* V0 的懸停邊框色 */
}

/* V0 風格的卡片內容區域 */
.v0-card .card-content {
  padding: 1.5rem;  /* V0 的內容內距 */
}

.v0-card .card-header {
  padding: 1.5rem 1.5rem 0 1.5rem;  /* V0 的標題內距 */
}

.v0-card .card-footer {
  padding: 0 1.5rem 1.5rem 1.5rem;  /* V0 的底部內距 */
}

/* V0 風格的按鈕增強 - 精確還原 */
.btn-v0-primary {
  background-color: var(--nomad-primary-v0);
  color: white;
  border: none;
  border-radius: 0.5rem;  /* V0 使用較小的圓角 */
  padding: 0.625rem 1.25rem;  /* V0 使用較緊湊的內距 */
  font-weight: 500;  /* V0 使用中等字重 */
  font-size: 0.875rem;  /* V0 使用較小字體 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);  /* V0 的過渡曲線 */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);  /* V0 的細微陰影 */
}

.btn-v0-primary:hover {
  background-color: #014080;
  transform: translateY(-1px);  /* V0 使用較小的上升距離 */
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);  /* V0 的懸停陰影 */
}

/* DaisyUI 按鈕的 V0 風格覆蓋 */
.btn {
  border-radius: 0.5rem !important;  /* 統一圓角 */
  font-weight: 500 !important;  /* 統一字重 */
  font-size: 0.875rem !important;  /* 統一字體大小 */
  padding: 0.625rem 1.25rem !important;  /* 統一內距 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;  /* 統一過渡 */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;  /* 統一陰影 */
}

.btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06) !important;
}

/* V0 風格的徽章 */
.v0-badge {
  display: inline-flex;
  align-items: center;
  border-radius: var(--v0-radius);
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.v0-badge-default {
  background-color: var(--v0-primary);
  color: var(--v0-primary-foreground);
}

.v0-badge-secondary {
  background-color: var(--v0-secondary);
  color: var(--v0-secondary-foreground);
}

.v0-badge-outline {
  background-color: transparent;
  border: 1px solid var(--v0-border);
  color: var(--v0-foreground);
}

/* V0 風格的提示框 */
.v0-alert {
  border: 1px solid var(--v0-border);
  border-radius: var(--v0-radius-lg);
  padding: 1rem;
  margin: 0.5rem 0;
}

.v0-alert-info {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1e40af;
}

.v0-alert-success {
  border-color: #10b981;
  background-color: #ecfdf5;
  color: #065f46;
}

.v0-alert-warning {
  border-color: #f59e0b;
  background-color: #fffbeb;
  color: #92400e;
}

.v0-alert-error {
  border-color: #ef4444;
  background-color: #fef2f2;
  color: #991b1b;
}

/* V0 風格的漸層背景 */
.v0-gradient-hero {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #7c3aed 100%);
}

.v0-gradient-page {
  background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f3e8ff 100%);
}

/* V0 風格的動畫 */
.v0-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.v0-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* V0 風格的文字樣式 */
.v0-text-balance {
  text-wrap: balance;
}

.v0-text-pretty {
  text-wrap: pretty;
}

/* 確保與 DaisyUI 的相容性 */
.btn.btn-v0-primary {
  background-color: var(--nomad-primary-v0) !important;
  border-color: var(--nomad-primary-v0) !important;
}

.btn.btn-v0-primary:hover {
  background-color: #014080 !important;
  border-color: #014080 !important;
}

/* DaisyUI 主題覆蓋 - 確保 V0 風格生效 */
[data-theme="light"] {
  --p: 210 100% 30%;  /* 覆蓋 DaisyUI 的 primary 色彩 */
  --pf: 0 0% 100%;    /* primary foreground */
  --pc: 0 0% 100%;    /* primary content */
}

/* 強制 V0 風格的卡片樣式 */
.v0-card {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
}

/* 強制 V0 風格的懸停效果 */
.v0-hover-lift:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
}

/* 強制 V0 風格的漸層背景 */
.v0-gradient-hero {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #7c3aed 100%) !important;
}

.v0-gradient-page {
  background: linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #f3e8ff 100%) !important;
}

/* Hero Section 專用樣式 */
.v0-gradient-hero h1 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;  /* 文字陰影增強可讀性 */
}

.v0-gradient-hero p {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;  /* 附標題陰影 */
}

/* Hero Section 按鈕樣式 */
.v0-hero-button {
  background-color: white !important;
  color: #1f2937 !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;  /* text-lg */
  padding: 1rem 2rem !important;   /* px-8 py-4 */
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.v0-hero-button:hover {
  background-color: #f9fafb !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}
