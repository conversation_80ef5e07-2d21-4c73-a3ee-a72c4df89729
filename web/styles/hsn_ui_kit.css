/* HSN UI Kit - 純 CSS 實現的現代化元件庫 */
/* 基於 HSN 設計系統 + Tailwind CDN，無需 Node.js */

/* ===== 按鈕元件 ===== */
.hsn-btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer border border-transparent;
  min-height: 2.5rem;
}

.hsn-btn:hover {
  @apply transform scale-105;
}

.hsn-btn:active {
  @apply transform scale-95;
}

/* 按鈕變體 */
.hsn-btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-4 focus:ring-blue-300;
}

.hsn-btn-secondary {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-4 focus:ring-green-300;
}

.hsn-btn-accent {
  @apply bg-amber-500 text-white hover:bg-amber-600 focus:ring-4 focus:ring-amber-300;
}

.hsn-btn-success {
  @apply bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-4 focus:ring-emerald-300;
}

.hsn-btn-warning {
  @apply bg-orange-500 text-white hover:bg-orange-600 focus:ring-4 focus:ring-orange-300;
}

.hsn-btn-error {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-4 focus:ring-red-300;
}

.hsn-btn-outline {
  @apply bg-transparent border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400;
}

.hsn-btn-ghost {
  @apply bg-transparent text-gray-700 hover:bg-gray-100;
}

.hsn-btn-link {
  @apply bg-transparent text-blue-600 hover:text-blue-800 hover:underline;
}

/* 按鈕尺寸 */
.hsn-btn-xs {
  @apply px-2 py-1 text-xs;
  min-height: 1.5rem;
}

.hsn-btn-sm {
  @apply px-3 py-1.5 text-sm;
  min-height: 2rem;
}

.hsn-btn-lg {
  @apply px-6 py-3 text-lg;
  min-height: 3rem;
}

.hsn-btn-xl {
  @apply px-8 py-4 text-xl;
  min-height: 3.5rem;
}

/* ===== 卡片元件 ===== */
.hsn-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-200;
}

.hsn-card:hover {
  @apply shadow-lg;
}

.hsn-card-body {
  @apply p-6;
}

.hsn-card-title {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.hsn-card-text {
  @apply text-gray-600 mb-4;
}

.hsn-card-actions {
  @apply flex gap-2 mt-4;
}

.hsn-card-actions.justify-end {
  @apply justify-end;
}

.hsn-card-actions.justify-center {
  @apply justify-center;
}

.hsn-card-actions.justify-between {
  @apply justify-between;
}

/* 卡片變體 */
.hsn-card-primary {
  @apply bg-blue-600 text-white;
}

.hsn-card-primary .hsn-card-title {
  @apply text-white;
}

.hsn-card-primary .hsn-card-text {
  @apply text-blue-100;
}

/* ===== 警告框元件 ===== */
.hsn-alert {
  @apply flex items-center p-4 rounded-lg border-l-4;
}

.hsn-alert-icon {
  @apply flex-shrink-0 w-5 h-5 mr-3;
}

.hsn-alert-content {
  @apply flex-1;
}

.hsn-alert-info {
  @apply bg-blue-50 border-blue-400 text-blue-800;
}

.hsn-alert-info .hsn-alert-icon {
  @apply text-blue-400;
}

.hsn-alert-success {
  @apply bg-green-50 border-green-400 text-green-800;
}

.hsn-alert-success .hsn-alert-icon {
  @apply text-green-400;
}

.hsn-alert-warning {
  @apply bg-yellow-50 border-yellow-400 text-yellow-800;
}

.hsn-alert-warning .hsn-alert-icon {
  @apply text-yellow-400;
}

.hsn-alert-error {
  @apply bg-red-50 border-red-400 text-red-800;
}

.hsn-alert-error .hsn-alert-icon {
  @apply text-red-400;
}

/* ===== 表單元件 ===== */
.hsn-form-control {
  @apply mb-4;
}

.hsn-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.hsn-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
}

.hsn-input:hover {
  @apply border-gray-400;
}

.hsn-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 resize-vertical;
}

.hsn-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 bg-white;
}

.hsn-checkbox {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
}

.hsn-radio {
  @apply w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2;
}

/* ===== 導航列元件 ===== */
.hsn-navbar {
  @apply flex items-center justify-between px-6 py-4 bg-white shadow-sm;
}

.hsn-navbar-brand {
  @apply text-xl font-bold text-gray-900;
}

.hsn-navbar-nav {
  @apply flex items-center space-x-6;
}

.hsn-navbar-link {
  @apply text-gray-600 hover:text-gray-900 transition-colors duration-200;
}

.hsn-navbar-link.active {
  @apply text-blue-600 font-medium;
}

/* ===== 徽章元件 ===== */
.hsn-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.hsn-badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.hsn-badge-secondary {
  @apply bg-green-100 text-green-800;
}

.hsn-badge-accent {
  @apply bg-amber-100 text-amber-800;
}

.hsn-badge-success {
  @apply bg-emerald-100 text-emerald-800;
}

.hsn-badge-warning {
  @apply bg-orange-100 text-orange-800;
}

.hsn-badge-error {
  @apply bg-red-100 text-red-800;
}

.hsn-badge-outline {
  @apply bg-transparent border border-gray-300 text-gray-700;
}

/* ===== 工具類別 ===== */
.hsn-shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.hsn-shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hsn-shadow-strong {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== 響應式工具 ===== */
@media (max-width: 768px) {
  .hsn-navbar {
    @apply px-4 py-3;
  }
  
  .hsn-navbar-nav {
    @apply space-x-4;
  }
  
  .hsn-card-body {
    @apply p-4;
  }
  
  .hsn-btn {
    @apply px-3 py-2 text-sm;
  }
}
