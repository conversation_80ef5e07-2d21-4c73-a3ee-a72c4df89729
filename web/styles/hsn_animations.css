/* =======================================
 * HSN 動畫效果系統
 * ======================================= */

/* === 基礎動畫 === */

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulseNomad {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 10px var(--color-interaction);
  }
  50% {
    box-shadow: 0 0 20px var(--color-interaction), 0 0 30px var(--color-interaction);
  }
}

/* === 進場動畫 === */

.animate-fade-in {
  animation: fadeIn var(--anim-fast) ease-out;
}

.animate-slide-in-right {
  animation: slideInRight var(--anim-fast) ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft var(--anim-fast) ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: glow 2s ease-in-out infinite;
}

/* === 響應式動畫 === */

/* 在較慢的設備上減少動畫 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 移動設備優化 */
@media (max-width: 768px) {
  .hover-float:hover {
    transform: none;
  }

  .btn-nomad-tech:hover::before {
    left: 0;
  }
}