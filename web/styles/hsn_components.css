/* =======================================
 * 遊牧好點組件樣式庫 (基於 HSN)
 * ======================================= */

/* === 按鈕系統 === */

/* 主要按鈕 - 保留專業感 + HSN 發光效果 */
.btn-nomad-primary {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: none;
  font-weight: 600;
  transition: all var(--anim-fast);
  cursor: pointer;
}

.btn-nomad-primary:hover {
  background: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(1, 88, 155, 0.3);
}

/* 科技感按鈕 - HSN 霓虹效果 */
.btn-nomad-tech {
  background: linear-gradient(135deg, var(--color-primary), var(--color-tech));
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: none;
  font-weight: 600;
  transition: all var(--anim-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-nomad-tech:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-neon);
}

.btn-nomad-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--anim-normal);
}

.btn-nomad-tech:hover::before {
  left: 100%;
}

/* 工作空間強調按鈕 */
.btn-nomad-workspace {
  background: var(--color-workspace);
  color: #1f2937;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: none;
  font-weight: 600;
  transition: all var(--anim-fast);
  cursor: pointer;
}

.btn-nomad-workspace:hover {
  background: var(--color-amber-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-amber);
}

/* 次要按鈕 */
.btn-nomad-secondary {
  background: transparent;
  color: var(--color-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-md);
  font-weight: 600;
  transition: all var(--anim-fast);
  cursor: pointer;
}

.btn-nomad-secondary:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px);
}

/* === 卡片系統 === */

/* 工作空間卡片 */
.workspace-card-nomad {
  background: var(--bg-primary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--anim-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.workspace-card-nomad:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(1, 88, 155, 0.15);
  border-color: var(--color-interaction);
}

.workspace-card-nomad::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-interaction));
  transform: scaleX(0);
  transition: transform var(--anim-fast);
}

.workspace-card-nomad:hover::before {
  transform: scaleX(1);
}

/* 特色工作空間卡片 */
.workspace-card-featured {
  background: linear-gradient(135deg, rgba(0, 255, 65, 0.05), rgba(0, 255, 255, 0.05));
  border: 2px solid var(--color-interaction);
  position: relative;
}

.workspace-card-featured::after {
  content: '✨ 推薦';
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--color-interaction);
  color: #1f2937;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 700;
}

/* === 聊天介面 === */

/* 聊天容器 */
.chat-container-nomad {
  background: var(--bg-primary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

/* 使用者訊息 */
.chat-bubble-user {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--spacing-xs);
  max-width: 80%;
  margin-left: auto;
  margin-bottom: var(--spacing-sm);
  animation: slideInRight var(--anim-fast);
}

/* AI 助理訊息 */
.chat-bubble-ai {
  background: var(--bg-secondary);
  color: var(--color-gray-800);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--spacing-xs);
  max-width: 80%;
  margin-right: auto;
  margin-bottom: var(--spacing-sm);
  border-left: 4px solid var(--color-interaction);
  animation: slideInLeft var(--anim-fast);
}

/* 輸入區域 */
.chat-input-container {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-gray-200);
}

.chat-input {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: all var(--anim-fast);
}

.chat-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(1, 88, 155, 0.1);
}

/* === 指示器系統 === */

/* Wi-Fi 信號指示器 */
.wifi-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.wifi-excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
}

.wifi-good {
  background: rgba(132, 204, 22, 0.1);
  color: #365314;
}

.wifi-fair {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.wifi-poor {
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
}

/* 評分星星 */
.rating-star {
  color: var(--color-gray-300);
  transition: color var(--anim-fast);
  cursor: pointer;
}

.rating-star.active {
  color: var(--color-workspace);
}

.rating-star:hover {
  color: var(--color-amber-secondary);
  transform: scale(1.1);
}

/* === 導航系統 === */

/* 主導航 */
.nav-nomad {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-md) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.nav-link-nomad {
  color: var(--color-gray-600);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  transition: all var(--anim-fast);
  font-weight: 500;
}

.nav-link-nomad:hover {
  color: var(--color-primary);
  background: rgba(1, 88, 155, 0.05);
}

.nav-link-nomad.active {
  color: var(--color-primary);
  background: rgba(1, 88, 155, 0.1);
}

/* === 載入狀態 === */

/* 載入指示器 */
.loading-nomad {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-gray-600);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin var(--anim-normal) linear infinite;
}

/* === 工具類別 === */

/* 主題容器 */
.theme-container {
  background-color: var(--bg-secondary);
  color: var(--color-gray-800);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark 主題容器 */
.theme-container-dark {
  background-color: #1f2937;
  color: #f3f4f6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* HSN 發光效果 */
.glow-neon {
  box-shadow: var(--shadow-glow-neon);
}

.glow-amber {
  box-shadow: var(--shadow-glow-amber);
}

/* 懸浮效果 */
.hover-float {
  transition: transform var(--anim-fast);
}

.hover-float:hover {
  transform: translateY(-4px);
}

/* 脈衝動畫 */
.pulse-nomad {
  animation: pulseNomad 2s ease-in-out infinite;
}

/* 漸變背景 */
.gradient-nomad-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
}

.gradient-nomad-tech {
  background: linear-gradient(135deg, var(--color-tech), var(--color-interaction));
}

.gradient-nomad-workspace {
  background: linear-gradient(135deg, var(--color-workspace), var(--color-amber-secondary));
}

/* === Dark Mode 專用樣式 === */

/* 專用的 dark mode 按鈕樣式 */
.btn-nomad-secondary-dark {
  background: transparent;
  color: var(--color-primary-light);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--color-primary-light);
  border-radius: var(--border-radius-md);
  font-weight: 600;
  transition: all var(--anim-fast);
  cursor: pointer;
}

.btn-nomad-secondary-dark:hover {
  background: var(--color-primary-light);
  color: var(--color-gray-900);
  transform: translateY(-2px);
}