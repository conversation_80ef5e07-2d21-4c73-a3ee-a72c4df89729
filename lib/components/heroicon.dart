import 'package:jaspr/jaspr.dart';

/// Heroicon component for displaying Heroicons in Jaspr
/// 
/// Usage:
/// - Heroicon.solid('home', size: 24) // For solid 24px icons
/// - Heroicon.outline('user', size: 24) // For outline 24px icons  
/// - Heroicon.mini('star', size: 20) // For solid 20px icons
class Heroicon extends StatelessComponent {
  final String name;
  final HeroiconStyle style;
  final int size;
  final String? classes;

  const Heroicon._(
    this.name, {
    required this.style,
    required this.size,
    this.classes,
    super.key,
  });

  /// Create a solid 24px Heroicon
  const Heroicon.solid(
    String name, {
    String? classes,
    Key? key,
  }) : this._(name, style: HeroiconStyle.solid24, size: 24, classes: classes, key: key);

  /// Create an outline 24px Heroicon
  const Heroicon.outline(
    String name, {
    String? classes,
    Key? key,
  }) : this._(name, style: HeroiconStyle.outline24, size: 24, classes: classes, key: key);

  /// Create a solid 20px Heroicon (mini)
  const Heroicon.mini(
    String name, {
    String? classes,
    Key? key,
  }) : this._(name, style: HeroiconStyle.solid20, size: 20, classes: classes, key: key);

  @override
  Iterable<Component> build(BuildContext context) sync* {
    // 根據樣式決定路徑
    final String stylePath = switch (style) {
      HeroiconStyle.solid24 => '24/solid',
      HeroiconStyle.outline24 => '24/outline',
      HeroiconStyle.solid20 => '20/solid',
    };

    // 構建 SVG URL
    final svgUrl = 'https://cdn.jsdelivr.net/npm/heroicons@2.2.0/$stylePath/$name.svg';

    // 構建 CSS 類別 - 如果外部有提供尺寸類別，則使用外部的；否則使用預設的
    final hasExternalSize = classes != null && (classes!.contains('w-') || classes!.contains('h-'));

    final defaultSizeClass = switch (size) {
      16 => 'w-4 h-4',   // 16px = 1rem = w-4
      20 => 'w-5 h-5',   // 20px = 1.25rem = w-5
      24 => 'w-6 h-6',   // 24px = 1.5rem = w-6
      _ => 'w-6 h-6',    // 預設為 24px
    };

    final allClasses = [
      'inline-block',
      if (!hasExternalSize) defaultSizeClass,
      if (classes != null) classes!,
    ].where((c) => c.isNotEmpty).join(' ');

    // 使用 img 標籤載入 SVG，並強制設定尺寸
    yield img(
      src: svgUrl,
      classes: allClasses,
      attributes: {
        'alt': '$name icon',
        'loading': 'lazy',
        'style': 'max-width: 100%; height: auto; display: inline-block;',
      },
    );
  }
}

enum HeroiconStyle {
  solid24('24-solid'),
  outline24('24-outline'),
  solid20('20-solid');

  const HeroiconStyle(this.suffix);
  final String suffix;
}

/// Common Heroicon names for easy reference
class HeroiconName {
  // Navigation
  static const String home = 'home';
  static const String user = 'user';
  static const String cog6Tooth = 'cog-6-tooth';
  static const String bars3 = 'bars-3';
  static const String xMark = 'x-mark';
  
  // Actions
  static const String plus = 'plus';
  static const String minus = 'minus';
  static const String trash = 'trash';
  static const String pencil = 'pencil';
  static const String check = 'check';
  
  // Arrows
  static const String arrowLeft = 'arrow-left';
  static const String arrowRight = 'arrow-right';
  static const String arrowUp = 'arrow-up';
  static const String arrowDown = 'arrow-down';
  static const String chevronLeft = 'chevron-left';
  static const String chevronRight = 'chevron-right';
  static const String chevronUp = 'chevron-up';
  static const String chevronDown = 'chevron-down';
  
  // Communication
  static const String envelope = 'envelope';
  static const String phone = 'phone';
  static const String chatBubbleLeft = 'chat-bubble-left';
  
  // Media
  static const String photo = 'photo';
  static const String play = 'play';
  static const String pause = 'pause';
  static const String stop = 'stop';
  
  // Status
  static const String exclamationTriangle = 'exclamation-triangle';
  static const String informationCircle = 'information-circle';
  static const String checkCircle = 'check-circle';
  static const String xCircle = 'x-circle';
  
  // Objects
  static const String magnifyingGlass = 'magnifying-glass';
  static const String heart = 'heart';
  static const String star = 'star';
  static const String bookmark = 'bookmark';
  static const String flag = 'flag';
  static const String key = 'key';
  static const String lock = 'lock-closed';
  static const String unlock = 'lock-open';
  
  // New in recent versions
  static const String paintBrush = 'paint-brush';
  static const String variable = 'variable';
  static const String wallet = 'wallet';
  static const String battery0 = 'battery-0';
  static const String battery50 = 'battery-50';
  static const String battery100 = 'battery-100';
  static const String cubeTransparent = 'cube-transparent';
  static const String currencyBangladeshi = 'currency-bangladeshi';
  static const String currencyDollar = 'currency-dollar';
  static const String currencyEuro = 'currency-euro';
  static const String currencyPound = 'currency-pound';
  static const String currencyRupee = 'currency-rupee';
  static const String currencyYen = 'currency-yen';
  
  // Text formatting (v2.1.5+)
  static const String bold = 'bold';
  static const String italic = 'italic';
  static const String underline = 'underline';
  static const String strikethrough = 'strikethrough';
  static const String h1 = 'h1';
  static const String h2 = 'h2';
  static const String h3 = 'h3';
  static const String numberedList = 'numbered-list';
  static const String linkSlash = 'link-slash';
  static const String slash = 'slash';
  static const String equals = 'equals';
  static const String divide = 'divide';
  static const String percentBadge = 'percent-badge';
  
  // Arrow turns (v2.1.5+)
  static const String arrowTurnLeftDown = 'arrow-turn-left-down';
  static const String arrowTurnLeftUp = 'arrow-turn-left-up';
  static const String arrowTurnRightDown = 'arrow-turn-right-down';
  static const String arrowTurnRightUp = 'arrow-turn-right-up';
  static const String arrowTurnUpLeft = 'arrow-turn-up-left';
  static const String arrowTurnUpRight = 'arrow-turn-up-right';
  
  // Calendar and date
  static const String calendarDateRange = 'calendar-date-range';
  static const String calendarDays = 'calendar-days';
  
  // Document currency
  static const String documentCurrencyDollar = 'document-currency-dollar';
  static const String documentCurrencyEuro = 'document-currency-euro';
  static const String documentCurrencyPound = 'document-currency-pound';
  static const String documentCurrencyBangladeshi = 'document-currency-bangladeshi';
  static const String documentCurrencyRupee = 'document-currency-rupee';
  static const String documentCurrencyYen = 'document-currency-yen';
}
