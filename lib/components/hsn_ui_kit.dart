import 'package:jaspr/jaspr.dart';

/// HSN UI Kit - 純 Dart 實現的現代化元件庫
/// 基於 HSN 設計系統 + Tailwind CDN，無需 Node.js

/// 按鈕元件
class HSNButton extends StatelessComponent {
  final String buttonText;
  final String variant;
  final String size;
  final VoidCallback? onPressed;
  final bool disabled;

  const HSNButton({
    required String text,
    this.variant = 'primary',
    this.size = 'md',
    this.onPressed,
    this.disabled = false,
  }) : buttonText = text;

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final classes = [
      'hsn-btn',
      'hsn-btn-$variant',
      if (size != 'md') 'hsn-btn-$size',
      if (disabled) 'opacity-50 cursor-not-allowed',
    ].join(' ');

    yield button(
      classes: classes,
      attributes: disabled ? {'disabled': 'true'} : {},
      events: disabled ? {} : {'click': (event) => onPressed?.call()},
      [text(buttonText)],
    );
  }
}

/// 卡片元件
class HSNCard extends StatelessComponent {
  final String? title;
  final String? content;
  final List<Component> actions;
  final String variant;
  final String actionsAlignment;

  const HSNCard({
    this.title,
    this.content,
    this.actions = const [],
    this.variant = 'default',
    this.actionsAlignment = 'end',
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final cardClasses = [
      'hsn-card',
      if (variant != 'default') 'hsn-card-$variant',
    ].join(' ');

    yield div(classes: cardClasses, [
      div(classes: 'hsn-card-body', [
        if (title != null)
          h2(classes: 'hsn-card-title', [text(title!)]),
        if (content != null)
          p(classes: 'hsn-card-text', [text(content!)]),
        if (actions.isNotEmpty)
          div(classes: 'hsn-card-actions justify-$actionsAlignment', actions),
      ]),
    ]);
  }
}

/// 警告框元件
class HSNAlert extends StatelessComponent {
  final String message;
  final String type;
  final bool showIcon;

  const HSNAlert({
    required this.message,
    this.type = 'info',
    this.showIcon = true,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'hsn-alert hsn-alert-$type', [
      if (showIcon) _buildIcon(),
      div(classes: 'hsn-alert-content', [
        span([text(message)]),
      ]),
    ]);
  }

  Component _buildIcon() {
    final iconPath = switch (type) {
      'success' => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      'warning' => 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z',
      'error' => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
      _ => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    };

    return svg(
      classes: 'hsn-alert-icon',
      attributes: {'fill': 'none', 'viewBox': '0 0 24 24', 'stroke': 'currentColor'},
      [
        path([], attributes: {
          'stroke-linecap': 'round',
          'stroke-linejoin': 'round',
          'stroke-width': '2',
          'd': iconPath,
        }),
      ],
    );
  }
}

/// 表單控制項元件
class HSNFormControl extends StatelessComponent {
  final String? label;
  final Component input;
  final String? helpText;

  const HSNFormControl({
    this.label,
    required this.input,
    this.helpText,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'hsn-form-control', [
      if (label != null)
        DomComponent(
          tag: 'label',
          classes: 'hsn-label',
          children: [text(label!)],
        ),
      input,
      if (helpText != null)
        p(classes: 'text-sm text-gray-500 mt-1', [text(helpText!)]),
    ]);
  }
}

/// 輸入框元件
class HSNInput extends StatelessComponent {
  final String? placeholder;
  final InputType type;
  final String? value;
  final bool disabled;

  const HSNInput({
    this.placeholder,
    this.type = InputType.text,
    this.value,
    this.disabled = false,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final attributes = <String, String>{
      if (placeholder != null) 'placeholder': placeholder!,
      if (value != null) 'value': value!,
      if (disabled) 'disabled': 'true',
    };

    yield input(
      type: type,
      classes: 'hsn-input',
      attributes: attributes,
    );
  }
}

/// 文字區域元件
class HSNTextarea extends StatelessComponent {
  final String? placeholder;
  final String? value;
  final int rows;
  final bool disabled;

  const HSNTextarea({
    this.placeholder,
    this.value,
    this.rows = 3,
    this.disabled = false,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final attributes = <String, String>{
      if (placeholder != null) 'placeholder': placeholder!,
      if (disabled) 'disabled': 'true',
      'rows': rows.toString(),
    };

    yield textarea(
      classes: 'hsn-textarea',
      attributes: attributes,
      [if (value != null) text(value!)],
    );
  }
}

/// 導航列元件
class HSNNavbar extends StatelessComponent {
  final String brandText;
  final List<HSNNavLink> links;
  final List<Component> actions;

  const HSNNavbar({
    required this.brandText,
    this.links = const [],
    this.actions = const [],
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield nav(classes: 'hsn-navbar', [
      div(classes: 'hsn-navbar-brand', [text(brandText)]),
      if (links.isNotEmpty)
        ul(classes: 'hsn-navbar-nav', [
          for (final link in links)
            li([
              a(
                href: link.href,
                classes: 'hsn-navbar-link ${link.active ? 'active' : ''}',
                [text(link.text)],
              ),
            ]),
        ]),
      if (actions.isNotEmpty)
        div(classes: 'flex items-center space-x-2', actions),
    ]);
  }
}

/// 導航連結資料類別
class HSNNavLink {
  final String text;
  final String href;
  final bool active;

  const HSNNavLink({
    required this.text,
    required this.href,
    this.active = false,
  });
}

/// 徽章元件
class HSNBadge extends StatelessComponent {
  final String badgeText;
  final String variant;
  final bool outline;

  const HSNBadge({
    required String text,
    this.variant = 'primary',
    this.outline = false,
  }) : badgeText = text;

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final classes = [
      'hsn-badge',
      if (outline) 'hsn-badge-outline' else 'hsn-badge-$variant',
    ].join(' ');

    yield span(classes: classes, [text(badgeText)]);
  }
}

/// 容器元件
class HSNContainer extends StatelessComponent {
  final List<Component> children;
  final String maxWidth;
  final bool centerContent;

  const HSNContainer({
    required this.children,
    this.maxWidth = '7xl',
    this.centerContent = true,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final classes = [
      'container',
      'max-w-$maxWidth',
      if (centerContent) 'mx-auto',
      'px-4',
      'sm:px-6',
      'lg:px-8',
    ].join(' ');

    yield div(classes: classes, children);
  }
}
