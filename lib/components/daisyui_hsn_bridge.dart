import 'package:jaspr/jaspr.dart';

/// DaisyUI + HSN 橋接元件庫
/// 結合 DaisyUI 的現代化設計與 HSN 的品牌特色

/// 現代化按鈕 - 結合 DaisyUI 與 HSN 樣式
class ModernButton extends StatelessComponent {
  final String text;
  final String variant;
  final String size;
  final VoidCallback? onPressed;
  final bool disabled;
  final String? icon;

  const ModernButton({
    required this.text,
    this.variant = 'primary',
    this.size = 'md',
    this.onPressed,
    this.disabled = false,
    this.icon,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final daisyClasses = [
      'btn',
      'btn-$variant',
      if (size != 'md') 'btn-$size',
      if (disabled) 'btn-disabled',
    ];

    final hsnClasses = [
      'hsn-btn-enhanced', // HSN 增強樣式
    ];

    final allClasses = [...daisyClasses, ...hsnClasses].join(' ');

    yield button(
      classes: allClasses,
      attributes: disabled ? {'disabled': 'true'} : {},
      events: disabled ? {} : {'click': (event) => onPressed?.call()},
      [
        if (icon != null) span(classes: 'mr-2', [DomComponent(tag: 'span', children: [Text(icon!)])]),
        DomComponent(tag: 'span', children: [Text(text)]),
      ],
    );
  }
}

/// 現代化卡片 - DaisyUI card + HSN 品牌色彩
class ModernCard extends StatelessComponent {
  final String? title;
  final String? subtitle;
  final List<Component> children;
  final List<Component> actions;
  final String variant;
  final String? imageUrl;
  final String? badge;

  const ModernCard({
    this.title,
    this.subtitle,
    this.children = const [],
    this.actions = const [],
    this.variant = 'default',
    this.imageUrl,
    this.badge,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final cardClasses = [
      'card',
      'bg-base-100',
      'shadow-xl',
      'hover:shadow-2xl',
      'transition-shadow',
      'duration-300',
      if (variant == 'featured') 'bg-gradient-to-br from-primary to-secondary text-primary-content',
      'hsn-card-enhanced', // HSN 增強樣式
    ].join(' ');

    yield div(classes: cardClasses, [
      // 圖片區域
      if (imageUrl != null)
        figure(classes: 'px-6 pt-6', [
          div(classes: 'w-full h-48 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl flex items-center justify-center', [
            span(classes: 'text-4xl', [text(imageUrl!)]) // 使用 emoji 作為圖片
          ])
        ]),
      
      // 卡片內容
      div(classes: 'card-body', [
        // 標題區域
        if (title != null)
          div(classes: 'flex items-center justify-between mb-2', [
            h2(classes: 'card-title ${variant == 'featured' ? 'text-white' : 'text-base-content'}', [
              text(title!),
              if (badge != null)
                div(classes: 'badge badge-secondary', [text(badge!)]),
            ]),
          ]),
        
        // 副標題
        if (subtitle != null)
          p(classes: '${variant == 'featured' ? 'text-white/80' : 'text-base-content/70'} mb-4', [
            text(subtitle!)
          ]),
        
        // 自定義內容
        ...children,
        
        // 動作按鈕
        if (actions.isNotEmpty)
          div(classes: 'card-actions justify-end mt-4', actions),
      ]),
    ]);
  }
}

/// 現代化統計卡片
class ModernStats extends StatelessComponent {
  final List<StatItem> stats;
  final bool vertical;

  const ModernStats({
    required this.stats,
    this.vertical = false,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final statsClasses = [
      'stats',
      if (vertical) 'stats-vertical' else 'stats-horizontal',
      'shadow-lg',
      'bg-base-100',
      'hsn-stats-enhanced',
    ].join(' ');

    yield div(classes: statsClasses, [
      for (final stat in stats)
        div(classes: 'stat', [
          div(classes: 'stat-figure text-${stat.color}', [
            span(classes: 'text-4xl', [text(stat.icon)]),
          ]),
          div(classes: 'stat-title', [text(stat.title)]),
          div(classes: 'stat-value text-${stat.color}', [text(stat.value)]),
          div(classes: 'stat-desc', [text(stat.description)]),
        ]),
    ]);
  }
}

/// 統計項目資料類別
class StatItem {
  final String title;
  final String value;
  final String description;
  final String icon;
  final String color;

  const StatItem({
    required this.title,
    required this.value,
    required this.description,
    required this.icon,
    this.color = 'primary',
  });
}

/// 現代化導航列
class ModernNavbar extends StatelessComponent {
  final String brandText;
  final String brandIcon;
  final List<NavItem> navItems;
  final List<Component> actions;

  const ModernNavbar({
    required this.brandText,
    this.brandIcon = '',
    this.navItems = const [],
    this.actions = const [],
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'navbar bg-base-100 shadow-lg hsn-navbar-enhanced', [
      // 品牌區域
      div(classes: 'navbar-start', [
        a(href: '/', classes: 'btn btn-ghost text-xl font-bold text-primary', [
          text(brandIcon.isEmpty ? brandText : '$brandIcon $brandText')
        ]),
      ]),
      
      // 導航選單（桌面版）
      div(classes: 'navbar-center hidden lg:flex', [
        ul(classes: 'menu menu-horizontal px-1', [
          for (final item in navItems)
            li([
              a(href: item.href, classes: 'btn btn-ghost', [text(item.text)])
            ]),
        ]),
      ]),
      
      // 動作區域
      div(classes: 'navbar-end', actions),
    ]);
  }
}

/// 導航項目資料類別
class NavItem {
  final String text;
  final String href;
  final bool active;

  const NavItem({
    required this.text,
    required this.href,
    this.active = false,
  });
}

/// 現代化警告框
class ModernAlert extends StatelessComponent {
  final String message;
  final String type;
  final String? icon;

  const ModernAlert({
    required this.message,
    this.type = 'info',
    this.icon,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final alertIcon = icon ?? _getDefaultIcon(type);
    
    yield div(classes: 'alert alert-$type hsn-alert-enhanced', [
      span([text('$alertIcon $message')]),
    ]);
  }

  String _getDefaultIcon(String type) {
    return switch (type) {
      'success' => '[OK]',
      'warning' => '[!]',
      'error' => '[X]',
      _ => '[i]',
    };
  }
}

/// 現代化徽章
class ModernBadge extends StatelessComponent {
  final String text;
  final String variant;
  final bool outline;
  final String? icon;

  const ModernBadge({
    required this.text,
    this.variant = 'primary',
    this.outline = false,
    this.icon,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final badgeClasses = [
      'badge',
      if (outline) 'badge-outline' else 'badge-$variant',
      'hsn-badge-enhanced',
    ].join(' ');

    yield div(classes: badgeClasses, [
      if (icon != null) DomComponent(tag: 'span', children: [Text('$icon ')]),
      DomComponent(tag: 'span', children: [Text(text)]),
    ]);
  }
}

/// 現代化表單控制項
class ModernFormControl extends StatelessComponent {
  final String? label;
  final Component input;
  final String? helpText;
  final String? errorText;

  const ModernFormControl({
    this.label,
    required this.input,
    this.helpText,
    this.errorText,
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'form-control hsn-form-enhanced', [
      if (label != null)
        DomComponent(
          tag: 'label',
          classes: 'label',
          children: [
            span(classes: 'label-text', [Text(label!)])
          ],
        ),
      input,
      if (helpText != null)
        DomComponent(
          tag: 'label',
          classes: 'label',
          children: [
            span(classes: 'label-text-alt', [Text(helpText!)])
          ],
        ),
      if (errorText != null)
        DomComponent(
          tag: 'label',
          classes: 'label',
          children: [
            span(classes: 'label-text-alt text-error', [Text(errorText!)])
          ],
        ),
    ]);
  }
}

/// 現代化輸入框
class ModernInput extends StatelessComponent {
  final String? placeholder;
  final InputType type;
  final String? value;
  final bool disabled;
  final String size;

  const ModernInput({
    this.placeholder,
    this.type = InputType.text,
    this.value,
    this.disabled = false,
    this.size = 'md',
    super.key,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    final inputClasses = [
      'input',
      'input-bordered',
      if (size != 'md') 'input-$size',
      'hsn-input-enhanced',
    ].join(' ');

    final attributes = <String, String>{
      if (placeholder != null) 'placeholder': placeholder!,
      if (value != null) 'value': value!,
      if (disabled) 'disabled': 'true',
    };

    yield input(
      type: type,
      classes: inputClasses,
      attributes: attributes,
    );
  }
}
