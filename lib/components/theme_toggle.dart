import 'package:jaspr/jaspr.dart';

import 'heroicon.dart';

/// 純 Dart 實現的主題切換組件
/// 遵循 Jaspr 最佳實踐，使用狀態管理和 CSS 類別
@client
class ThemeToggle extends StatefulComponent {
  const ThemeToggle({super.key});

  @override
  State<ThemeToggle> createState() => _ThemeToggleState();
}

class _ThemeToggleState extends State<ThemeToggle> {
  String currentTheme = 'light';

  @override
  void initState() {
    super.initState();
    // 初始化主題狀態
    print('ThemeToggle initialized');
  }

  void toggleTheme() {
    setState(() {
      currentTheme = currentTheme == 'light' ? 'dark' : 'light';
    });

    print('Theme toggled to: $currentTheme');
  }

  @override
  Iterable<Component> build(BuildContext context) sync* {
    // 根據當前主題動態設定按鈕樣式
    final buttonClasses = currentTheme == 'dark'
        ? 'btn-nomad-secondary-dark px-3 py-2 text-sm flex items-center gap-2'
        : 'btn-nomad-secondary px-3 py-2 text-sm flex items-center gap-2';

    yield button(
      id: 'theme-toggle',
      classes: buttonClasses,
      onClick: () => toggleTheme(),
      [
        // 使用 HSN 整合的 HeroIcon
        Heroicon.outline(HeroiconName.cog6Tooth, classes: 'w-4 h-4'),
        text(currentTheme == 'light' ? '切換深色模式' : '切換淺色模式')
      ]
    );
  }
}
