import 'package:jaspr/jaspr.dart';

/// DaisyUI 純 CDN 整合示例頁面
/// 展示 DaisyUI 與 HSN 設計系統的完美結合
class DaisyUIDemoPage extends StatelessComponent {
  const DaisyUIDemoPage({super.key});

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'min-h-screen bg-base-100 p-8', [
      // 頁面標題
      div(classes: 'text-center mb-12', [
        h1(classes: 'text-4xl font-bold text-primary mb-4', [
          text('🎨 DaisyUI + HSN 整合示例')
        ]),
        p(classes: 'text-lg text-base-content opacity-70', [
          text('純 CDN 方式，無需 Node.js 環境')
        ]),
      ]),

      // 按鈕示例區塊
      _buildButtonSection(),
      
      // 卡片示例區塊
      _buildCardSection(),
      
      // 表單示例區塊
      _buildFormSection(),
      
      // 警告框示例區塊
      _buildAlertSection(),
      
      // 導航示例區塊
      _buildNavigationSection(),
    ]);
  }

  /// 按鈕示例區塊
  Component _buildButtonSection() {
    return div(classes: 'mb-16', [
      h2(classes: 'text-2xl font-bold mb-6 text-secondary', [
        text('🔘 按鈕元件')
      ]),
      div(classes: 'grid grid-cols-1 md:grid-cols-3 gap-6', [
        // 基本按鈕
        div(classes: 'card bg-base-200 shadow-xl p-6', [
          h3(classes: 'card-title mb-4', [text('基本按鈕')]),
          div(classes: 'space-y-3', [
            button(classes: 'btn btn-primary', [text('主要按鈕')]),
            button(classes: 'btn btn-secondary', [text('次要按鈕')]),
            button(classes: 'btn btn-accent', [text('強調按鈕')]),
          ]),
        ]),
        
        // 按鈕變體
        div(classes: 'card bg-base-200 shadow-xl p-6', [
          h3(classes: 'card-title mb-4', [text('按鈕變體')]),
          div(classes: 'space-y-3', [
            button(classes: 'btn btn-outline btn-primary', [text('外框按鈕')]),
            button(classes: 'btn btn-ghost', [text('幽靈按鈕')]),
            button(classes: 'btn btn-link', [text('連結按鈕')]),
          ]),
        ]),
        
        // 按鈕尺寸
        div(classes: 'card bg-base-200 shadow-xl p-6', [
          h3(classes: 'card-title mb-4', [text('按鈕尺寸')]),
          div(classes: 'space-y-3', [
            button(classes: 'btn btn-primary btn-lg', [text('大按鈕')]),
            button(classes: 'btn btn-primary', [text('標準按鈕')]),
            button(classes: 'btn btn-primary btn-sm', [text('小按鈕')]),
          ]),
        ]),
      ]),
    ]);
  }

  /// 卡片示例區塊
  Component _buildCardSection() {
    return div(classes: 'mb-16', [
      h2(classes: 'text-2xl font-bold mb-6 text-secondary', [
        text('🃏 卡片元件')
      ]),
      div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6', [
        // 基本卡片
        div(classes: 'card bg-base-100 shadow-xl', [
          div(classes: 'card-body', [
            h2(classes: 'card-title', [text('基本卡片')]),
            p([text('這是一個使用 DaisyUI 的基本卡片元件。')]),
            div(classes: 'card-actions justify-end', [
              button(classes: 'btn btn-primary', [text('了解更多')]),
            ]),
          ]),
        ]),
        
        // 圖片卡片
        div(classes: 'card bg-base-100 shadow-xl', [
          figure([
            div(classes: 'h-48 bg-gradient-to-r from-primary to-secondary flex items-center justify-center', [
              span(classes: 'text-white text-2xl', [text('🖼️')])
            ])
          ]),
          div(classes: 'card-body', [
            h2(classes: 'card-title', [text('圖片卡片')]),
            p([text('帶有圖片區域的卡片元件。')]),
            div(classes: 'card-actions justify-end', [
              button(classes: 'btn btn-primary', [text('查看')]),
            ]),
          ]),
        ]),
        
        // HSN 風格卡片
        div(classes: 'card bg-gradient-to-br from-nomad-primary to-nomad-tech shadow-xl text-white', [
          div(classes: 'card-body', [
            h2(classes: 'card-title', [text('HSN 風格卡片')]),
            p([text('結合 HSN 設計系統的漸層卡片。')]),
            div(classes: 'card-actions justify-end', [
              button(classes: 'btn btn-ghost text-white border-white', [text('探索')]),
            ]),
          ]),
        ]),
      ]),
    ]);
  }

  /// 表單示例區塊
  Component _buildFormSection() {
    return div(classes: 'mb-16', [
      h2(classes: 'text-2xl font-bold mb-6 text-secondary', [
        text('📝 表單元件')
      ]),
      div(classes: 'card bg-base-100 shadow-xl max-w-2xl mx-auto', [
        div(classes: 'card-body', [
          h3(classes: 'card-title mb-6', [text('聯絡表單')]),
          form([
            div(classes: 'grid grid-cols-1 md:grid-cols-2 gap-4 mb-4', [
              div(classes: 'form-control', [
                label(classes: 'label', [
                  span(classes: 'label-text', [text('姓名')])
                ]),
                input(classes: 'input input-bordered', attributes: {
                  'type': 'text',
                  'placeholder': '請輸入您的姓名'
                }),
              ]),
              div(classes: 'form-control', [
                label(classes: 'label', [
                  span(classes: 'label-text', [text('電子郵件')])
                ]),
                input(classes: 'input input-bordered', attributes: {
                  'type': 'email',
                  'placeholder': '請輸入電子郵件'
                }),
              ]),
            ]),
            div(classes: 'form-control mb-4', [
              label(classes: 'label', [
                span(classes: 'label-text', [text('主題')])
              ]),
              select(classes: 'select select-bordered', [
                option([text('請選擇主題')]),
                option([text('一般詢問')]),
                option([text('技術支援')]),
                option([text('合作提案')]),
              ]),
            ]),
            div(classes: 'form-control mb-6', [
              label(classes: 'label', [
                span(classes: 'label-text', [text('訊息內容')])
              ]),
              textarea([text('')], classes: 'textarea textarea-bordered h-24', attributes: {
                'placeholder': '請輸入您的訊息...'
              }),
            ]),
            div(classes: 'form-control', [
              button(classes: 'btn btn-primary', [text('送出訊息')]),
            ]),
          ]),
        ]),
      ]),
    ]);
  }

  /// 警告框示例區塊
  Component _buildAlertSection() {
    return div(classes: 'mb-16', [
      h2(classes: 'text-2xl font-bold mb-6 text-secondary', [
        text('⚠️ 警告框元件')
      ]),
      div(classes: 'space-y-4 max-w-2xl mx-auto', [
        div(classes: 'alert alert-info', [
          span([text('ℹ️ 這是一個資訊提示框。')])
        ]),
        div(classes: 'alert alert-success', [
          span([text('✅ 操作成功完成！')])
        ]),
        div(classes: 'alert alert-warning', [
          span([text('⚠️ 請注意這個重要提醒。')])
        ]),
        div(classes: 'alert alert-error', [
          span([text('❌ 發生錯誤，請重試。')])
        ]),
      ]),
    ]);
  }

  /// 導航示例區塊
  Component _buildNavigationSection() {
    return div(classes: 'mb-16', [
      h2(classes: 'text-2xl font-bold mb-6 text-secondary', [
        text('🧭 導航元件')
      ]),
      div(classes: 'space-y-8', [
        // 標籤頁
        div(classes: 'card bg-base-100 shadow-xl p-6', [
          h3(classes: 'text-lg font-bold mb-4', [text('標籤頁')]),
          div(classes: 'tabs tabs-boxed', [
            a([text('首頁')], href: '#', classes: 'tab tab-active'),
            a([text('關於')], href: '#', classes: 'tab'),
            a([text('服務')], href: '#', classes: 'tab'),
            a([text('聯絡')], href: '#', classes: 'tab'),
          ]),
        ]),
        
        // 麵包屑
        div(classes: 'card bg-base-100 shadow-xl p-6', [
          h3(classes: 'text-lg font-bold mb-4', [text('麵包屑導航')]),
          div(classes: 'breadcrumbs text-sm', [
            ul([
              li([a([text('首頁')], href: '#')]),
              li([a([text('產品')], href: '#')]),
              li([text('DaisyUI 示例')]),
            ])
          ]),
        ]),
        
        // 步驟指示器
        div(classes: 'card bg-base-100 shadow-xl p-6', [
          h3(classes: 'text-lg font-bold mb-4', [text('步驟指示器')]),
          ul(classes: 'steps steps-horizontal', [
            li(classes: 'step step-primary', [text('選擇')]),
            li(classes: 'step step-primary', [text('配置')]),
            li(classes: 'step', [text('完成')]),
          ]),
        ]),
      ]),
    ]);
  }
}
