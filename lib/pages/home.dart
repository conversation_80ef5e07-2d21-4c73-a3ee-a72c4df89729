import 'package:jaspr/jaspr.dart';

// By using the @client annotation this component will be automatically compiled to javascript and mounted
// on the client. Therefore:
// - this file and any imported file must be compilable for both server and client environments.
// - this component and any child components will be built once on the server during pre-rendering and then
//   again on the client during normal rendering.
@client
class Home extends StatefulComponent {
  const Home({super.key});

  @override
  State<Home> createState() => HomeState();
}

class HomeState extends State<Home> {
  String currentTheme = 'light';

  @override
  void initState() {
    super.initState();
    // Run code depending on the rendering environment.
    if (kIsWeb) {
      print("Hello client");
    } else {
      print("Hello server");
    }
  }

  void toggleTheme() {
    setState(() {
      currentTheme = currentTheme == 'light' ? 'dark' : 'light';
    });
    print('Theme toggled to: $currentTheme');
  }

  @override
  Iterable<Component> build(BuildContext context) sync* {
    // 使用 DaisyUI 主題系統，根據主題動態設定
    final themeAttribute = currentTheme == 'dark' ? 'dark' : 'light';

    yield div(classes: 'min-h-screen bg-base-100', attributes: {'data-theme': themeAttribute}, [

      // 現代化導航列
      div(classes: 'navbar bg-base-100 shadow-lg', [
        div(classes: 'navbar-start', [
          // Logo 和品牌名稱
          a(href: '/', classes: 'btn btn-ghost text-xl font-bold text-primary', [
            text('遊牧好點')
          ]),
        ]),
        div(classes: 'navbar-center hidden lg:flex', [
          ul(classes: 'menu menu-horizontal px-1', [
            li([a(href: '#features', classes: 'btn btn-ghost', [text('特色功能')])]),
            li([a(href: '#workspaces', classes: 'btn btn-ghost', [text('工作空間')])]),
            li([a(href: '/about', classes: 'btn btn-ghost', [text('關於我們')])]),
          ]),
        ]),
        div(classes: 'navbar-end', [
          // 主題切換按鈕 - 使用 DaisyUI 樣式
          button(
            classes: 'btn btn-ghost btn-circle',
            onClick: () => toggleTheme(),
            [
              text('主題'),
            ]
          ),
          // DaisyUI 示例按鈕
          a(href: '/daisyui-demo', classes: 'btn btn-outline btn-primary ml-2', [
            text('UI 示例')
          ]),
          // 現代化版本按鈕
          a(href: '/modern', classes: 'btn btn-primary ml-2', [
            text('現代化版本')
          ]),
        ]),
      ]),

      // Hero 區塊 - 使用 DaisyUI Hero 元件
      div(classes: 'hero min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10', [
        div(classes: 'hero-content text-center', [
          div(classes: 'max-w-4xl', [
            // 主標題
            h1(classes: 'text-6xl font-bold text-base-content mb-6', [
              span(classes: 'text-primary', [text('遊牧')]),
              text('好點'),
            ]),

            // 副標題
            p(classes: 'text-xl text-base-content/70 mb-8 max-w-2xl mx-auto', [
              text('探索台灣最佳數位遊牧工作空間，享受現代化設計體驗！')
            ]),

            // 特色功能卡片 - 使用 DaisyUI Stats
            div(id: 'features', classes: 'stats stats-vertical lg:stats-horizontal shadow-lg bg-base-100 mb-12', [
              div(classes: 'stat', [
                div(classes: 'stat-figure text-warning', [
                  div(classes: 'w-12 h-12 bg-warning rounded-full flex items-center justify-center text-warning-content font-bold', [
                    text('WiFi')
                  ]),
                ]),
                div(classes: 'stat-title', [text('網路品質')]),
                div(classes: 'stat-value text-warning', [text('高速')]),
                div(classes: 'stat-desc', [text('穩定連線保證')]),
              ]),
              div(classes: 'stat', [
                div(classes: 'stat-figure text-error', [
                  div(classes: 'w-12 h-12 bg-error rounded-full flex items-center justify-center text-error-content font-bold', [
                    text('舒適')
                  ]),
                ]),
                div(classes: 'stat-title', [text('環境品質')]),
                div(classes: 'stat-value text-error', [text('舒適')]),
                div(classes: 'stat-desc', [text('專業工作環境')]),
              ]),
              div(classes: 'stat', [
                div(classes: 'stat-figure text-success', [
                  div(classes: 'w-12 h-12 bg-success rounded-full flex items-center justify-center text-success-content font-bold', [
                    text('認證')
                  ]),
                ]),
                div(classes: 'stat-title', [text('服務品質')]),
                div(classes: 'stat-value text-success', [text('保證')]),
                div(classes: 'stat-desc', [text('精選認證空間')]),
              ]),
            ]),

            // 主要行動按鈕 - 使用 DaisyUI 按鈕
            div(classes: 'flex flex-wrap gap-4 justify-center', [
              button(classes: 'btn btn-primary btn-lg', [
                text('開始探索工作空間'),
              ]),
              button(classes: 'btn btn-secondary btn-lg', [
                text('AI 智能推薦'),
              ]),
              button(classes: 'btn btn-accent btn-lg btn-outline', [
                text('探索特色空間'),
              ]),
            ]),
          ]),
        ]),
      ]),

      // 工作空間展示區域 - 使用 DaisyUI 卡片
      div(id: 'workspaces', classes: 'py-20 bg-base-200', [
        div(classes: 'container mx-auto px-8', [
          // 區塊標題
          div(classes: 'text-center mb-16', [
            h2(classes: 'text-4xl font-bold text-base-content mb-4', [
              text('精選工作空間')
            ]),
            p(classes: 'text-lg text-base-content/70 max-w-2xl mx-auto', [
              text('我們精心挑選台灣各地最適合數位遊牧的工作空間，每個地點都經過實地評估')
            ]),
          ]),

          // 工作空間卡片網格
          div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8', [
            // 卡片 1 - 星巴克敦南
            div(classes: 'card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300', [
              figure(classes: 'px-6 pt-6', [
                div(classes: 'w-full h-48 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center', [
                  div(classes: 'text-white text-2xl font-bold', [text('COFFEE')])
                ])
              ]),
              div(classes: 'card-body', [
                div(classes: 'flex items-center mb-2', [
                  span(classes: 'badge badge-primary badge-outline', [text('台北大安區')])
                ]),
                h2(classes: 'card-title text-base-content', [text('星巴克 敦南門市')]),
                p(classes: 'text-base-content/70', [text('寬敞舒適的工作環境，提供高速 Wi-Fi 和充足插座')]),
                div(classes: 'flex items-center gap-4 mt-4', [
                  div(classes: 'badge badge-success gap-2', [
                    text('Wi-Fi 優秀')
                  ]),
                  div(classes: 'badge badge-secondary gap-2', [
                    text('咖啡供應')
                  ])
                ]),
                div(classes: 'card-actions justify-end mt-4', [
                  button(classes: 'btn btn-primary btn-sm', [text('查看詳情')])
                ])
              ])
            ]),

            // 卡片 2 - 微風南山（特色推薦）
            div(classes: 'card bg-gradient-to-br from-primary to-secondary text-primary-content shadow-xl hover:shadow-2xl transition-shadow duration-300', [
              figure(classes: 'px-6 pt-6', [
                div(classes: 'w-full h-48 bg-white/20 rounded-xl flex items-center justify-center', [
                  div(classes: 'text-white text-2xl font-bold', [text('OFFICE')])
                ])
              ]),
              div(classes: 'card-body', [
                div(classes: 'flex items-center mb-2', [
                  span(classes: 'badge badge-accent', [text('台北信義區')])
                ]),
                h2(classes: 'card-title text-white', [
                  text('微風南山 共享空間'),
                  div(classes: 'badge badge-warning', [text('推薦')])
                ]),
                p(classes: 'text-white/80', [text('現代化共享辦公空間，完美適合數位遊牧工作')]),
                div(classes: 'flex items-center gap-4 mt-4', [
                  div(classes: 'badge badge-accent gap-2', [
                    text('Wi-Fi 優秀')
                  ]),
                  div(classes: 'badge badge-warning gap-2', [
                    text('快速充電')
                  ])
                ]),
                div(classes: 'card-actions justify-end mt-4', [
                  button(classes: 'btn btn-accent btn-sm', [text('立即預約')])
                ])
              ])
            ]),

            // 卡片 3 - 誠品書店
            div(classes: 'card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300', [
              figure(classes: 'px-6 pt-6', [
                div(classes: 'w-full h-48 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center', [
                  div(classes: 'text-white text-2xl font-bold', [text('BOOKS')])
                ])
              ]),
              div(classes: 'card-body', [
                div(classes: 'flex items-center mb-2', [
                  span(classes: 'badge badge-primary badge-outline', [text('台北中山區')])
                ]),
                h2(classes: 'card-title text-base-content', [text('誠品書店 松菸店')]),
                p(classes: 'text-base-content/70', [text('書香環繞的工作環境，安靜舒適適合專注工作')]),
                div(classes: 'flex items-center gap-4 mt-4', [
                  div(classes: 'badge badge-info gap-2', [
                    text('Wi-Fi 良好')
                  ]),
                  div(classes: 'badge badge-neutral gap-2', [
                    text('閱讀氛圍')
                  ])
                ]),
                div(classes: 'card-actions justify-end mt-4', [
                  button(classes: 'btn btn-primary btn-sm', [text('查看詳情')])
                ])
              ])
            ])
          ])
        ])
      ]),

      // 現代化 Footer - 使用 DaisyUI Footer 元件
      footer(classes: 'footer footer-center p-10 bg-base-200 text-base-content rounded', [
        div(classes: 'grid grid-flow-col gap-4', [
          a(href: '/about', classes: 'link link-hover', [text('關於我們')]),
          a(href: '#', classes: 'link link-hover', [text('聯絡方式')]),
          a(href: '#', classes: 'link link-hover', [text('使用條款')]),
          a(href: '#', classes: 'link link-hover', [text('隱私政策')]),
        ]),
        div([
          div(classes: 'grid grid-flow-col gap-4', [
            a(href: '#', classes: 'btn btn-ghost btn-circle', [
              text('♡')
            ]),
            a(href: '#', classes: 'btn btn-ghost btn-circle', [
              text('★')
            ]),
            a(href: '/daisyui-demo', classes: 'btn btn-ghost btn-circle', [
              text('UI')
            ]),
          ])
        ]),
        div([
          p(classes: 'flex items-center gap-2', [
            text('Powered by '),
            span(classes: 'font-bold text-primary', [text('Jaspr')]),
            text(' + '),
            span(classes: 'font-bold text-secondary', [text('DaisyUI')]),
          ]),
          p(classes: 'text-sm opacity-70', [text('© 2024 遊牧好點 - 探索最佳工作空間')])
        ])
      ])
      ]);
  }
}
