import 'package:jaspr/jaspr.dart';
import '../components/hsn_ui_kit.dart';

/// HSN UI Kit 測試頁面
/// 展示純 Jaspr 方式的現代化 UI 元件
class DaisyUITestPage extends StatelessComponent {
  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'min-h-screen bg-gray-50 p-8', [
      HSNContainer(children: [

        // 頁面標題
        h1(classes: 'text-4xl font-bold text-center mb-8 text-gray-900', [
          text('HSN UI Kit - 純 Jaspr 現代化元件')
        ]),

        // 介紹文字
        div(classes: 'text-center mb-12', [
          p(classes: 'text-lg text-gray-600 mb-4', [
            text('基於 HSN 設計系統 + Tailwind CDN，無需 Node.js 的現代化 UI 元件庫')
          ]),
          HSNBadge(text: '✅ 純 Jaspr 方式', variant: 'success'),
          text(' '),
          HSNBadge(text: '🚀 無 Node.js 依賴', variant: 'primary'),
          text(' '),
          HSNBadge(text: '🎨 現代化設計', variant: 'accent'),
        ]),

        // 按鈕測試區塊
        HSNCard(
          title: '按鈕元件',
          content: '各種樣式和尺寸的按鈕元件',
          actions: [
            div(classes: 'w-full', [
              div(classes: 'flex flex-wrap gap-4 mb-4', [
                HSNButton(text: 'Primary', variant: 'primary'),
                HSNButton(text: 'Secondary', variant: 'secondary'),
                HSNButton(text: 'Accent', variant: 'accent'),
                HSNButton(text: 'Success', variant: 'success'),
                HSNButton(text: 'Warning', variant: 'warning'),
                HSNButton(text: 'Error', variant: 'error'),
              ]),
              div(classes: 'flex flex-wrap gap-4 mb-4', [
                HSNButton(text: 'Outline', variant: 'outline'),
                HSNButton(text: 'Ghost', variant: 'ghost'),
                HSNButton(text: 'Link', variant: 'link'),
              ]),
              div(classes: 'flex flex-wrap gap-4 items-center', [
                HSNButton(text: 'Extra Small', variant: 'primary', size: 'xs'),
                HSNButton(text: 'Small', variant: 'primary', size: 'sm'),
                HSNButton(text: 'Medium', variant: 'primary'),
                HSNButton(text: 'Large', variant: 'primary', size: 'lg'),
              ]),
            ]),
          ],
          actionsAlignment: 'start',
        ),

        div(classes: 'mb-8', []),

        // 警告框測試
        div(classes: 'space-y-4 mb-8', [
          HSNAlert(
            message: '這是一個資訊提示框 - HSN UI Kit 正常工作！',
            type: 'info',
          ),
          HSNAlert(
            message: 'Tailwind CSS + HSN UI Kit 整合成功！',
            type: 'success',
          ),
          HSNAlert(
            message: '請注意這個警告訊息。',
            type: 'warning',
          ),
          HSNAlert(
            message: '這是一個錯誤提示。',
            type: 'error',
          ),
        ]),

        // 卡片網格測試
        div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8', [
          HSNCard(
            title: '基本卡片',
            content: '這是一個使用 HSN UI Kit 的基本卡片元件。',
            actions: [
              HSNButton(text: '了解更多', variant: 'primary'),
            ],
          ),
          HSNCard(
            title: '主色調卡片',
            content: '這是一個使用主色調的卡片。',
            variant: 'primary',
            actions: [
              HSNButton(text: '立即行動', variant: 'secondary'),
            ],
          ),
          HSNCard(
            title: '帶徽章的卡片',
            content: '這個卡片包含了徽章元件。',
            actions: [
              div(classes: 'flex gap-2', [
                HSNBadge(text: 'NEW', variant: 'success'),
                HSNBadge(text: '標籤1', outline: true),
                HSNBadge(text: '標籤2', outline: true),
              ]),
            ],
            actionsAlignment: 'between',
          ),
        ]),

        // 表單測試
        HSNCard(
          title: '表單元件測試',
          content: '各種表單輸入元件的展示',
          actions: [
            form(classes: 'w-full space-y-4', [
              HSNFormControl(
                label: '姓名',
                input: HSNInput(placeholder: '請輸入您的姓名'),
              ),
              HSNFormControl(
                label: '電子郵件',
                input: HSNInput(
                  type: InputType.email,
                  placeholder: '請輸入您的電子郵件',
                ),
              ),
              HSNFormControl(
                label: '訊息',
                input: HSNTextarea(
                  placeholder: '請輸入您的訊息',
                  rows: 4,
                ),
              ),
              div(classes: 'flex items-center space-x-2', [
                input(type: InputType.checkbox, classes: 'hsn-checkbox'),
                span(classes: 'text-sm text-gray-700', [text('我同意服務條款')]),
              ]),
              div(classes: 'pt-4', [
                HSNButton(text: '送出表單', variant: 'primary'),
                text(' '),
                HSNButton(text: '重設', variant: 'outline'),
              ]),
            ]),
          ],
          actionsAlignment: 'start',
        ),

        div(classes: 'mb-8', []),

        // 導航列測試
        HSNCard(
          title: '導航列元件',
          content: '響應式導航列展示',
          actions: [
            HSNNavbar(
              brandText: 'Nomad Spot TW',
              links: [
                HSNNavLink(text: '首頁', href: '/', active: true),
                HSNNavLink(text: '關於我們', href: '/about'),
                HSNNavLink(text: 'HSN UI Kit', href: '/daisyui-test'),
                HSNNavLink(text: '服務', href: '#'),
              ],
              actions: [
                HSNButton(text: '開始使用', variant: 'primary', size: 'sm'),
              ],
            ),
          ],
          actionsAlignment: 'start',
        ),

        // 成功訊息
        div(classes: 'text-center bg-white rounded-lg p-8 hsn-shadow-soft', [
          h2(classes: 'text-2xl font-bold text-gray-900 mb-4', [
            text('🎉 HSN UI Kit 整合完成！')
          ]),
          p(classes: 'text-gray-600 mb-6', [
            text('純 Jaspr 方式的現代化 UI 元件庫，無需 Node.js，符合 Jaspr 設計理念。')
          ]),
          div(classes: 'flex justify-center gap-2 flex-wrap', [
            HSNBadge(text: '✅ 純 Dart 生態系統', variant: 'success'),
            HSNBadge(text: '🚀 無構建步驟', variant: 'primary'),
            HSNBadge(text: '🎨 現代化設計', variant: 'accent'),
            HSNBadge(text: '📱 響應式', variant: 'secondary'),
          ]),
        ]),
      ]),
    ]);
  }
}
