import 'package:jaspr/jaspr.dart';
import '../components/hsn_ui_kit.dart';

/// DaisyUI + HSN 測試頁面
/// 基於 V0 範例設計的 Jaspr 實作
class DaisyUITestPage extends StatelessComponent {
  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50', [

      // Hero Section - V0 風格漸層背景
      _buildHeroSection(),

      // 主要內容容器
      div(classes: 'container mx-auto px-4 py-12 space-y-16', [

        // 按鈕測試區域
        _buildButtonTestSection(),

        // 卡片測試區域
        _buildCardTestSection(),

        // 核心功能：遊牧指數視覺化
        _buildNomadScorecardSection(),

        // 提示訊息區域
        _buildAlertSection(),

        // 徽章測試區域
        _buildBadgeSection(),

        // 表單測試區域
        _buildFormSection(),
      ]),
    ]);
  }

  /// Hero Section - V0 風格
  Component _buildHeroSection() {
    return section(
      classes: 'relative overflow-hidden bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white',
      [
        // 背景遮罩
        div(classes: 'absolute inset-0 bg-black/10', []),

        // 內容
        div(classes: 'relative container mx-auto px-4 py-20 text-center', [
          h1(classes: 'text-5xl font-bold mb-4', [
            text('DaisyUI + HSN')
          ]),
          p(classes: 'text-xl mb-8 text-blue-100', [
            text('測試頁面 - 展示所有組件效果')
          ]),
          button(
            classes: 'btn btn-primary btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-300',
            [text('開始測試 🚀')]
          ),
          // 添加 DaisyUI 測試指示器
          div(classes: 'mt-8 text-sm opacity-75', [
            text('🔍 DaisyUI 狀態檢查：如果按鈕有藍色背景，則 DaisyUI 正常工作')
          ]),
        ]),
      ]
    );
  }

  /// 按鈕測試區域 - V0 風格
  Component _buildButtonTestSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('按鈕測試區域')
      ]),
      div(classes: 'grid md:grid-cols-3 gap-6', [

        // DaisyUI 基礎按鈕卡片
        _buildCard(
          title: 'DaisyUI 按鈕',
          content: [
            button(classes: 'btn btn-primary w-full mb-3', [text('Primary')]),
            button(classes: 'btn btn-secondary w-full mb-3', [text('Secondary')]),
            button(classes: 'btn btn-accent w-full mb-3', [text('Accent')]),
            button(classes: 'btn btn-success w-full mb-3', [text('Success')]),
            button(classes: 'btn btn-warning w-full mb-3', [text('Warning')]),
            button(classes: 'btn btn-error w-full', [text('Error')]),
          ]
        ),

        // HSN 風格按鈕卡片
        _buildCard(
          title: 'HSN 風格按鈕',
          content: [
            button(classes: 'btn w-full mb-3 bg-[#015899] hover:bg-[#014080] text-white border-none', [
              text('遊牧主色')
            ]),
            button(classes: 'btn w-full mb-3 bg-blue-500 hover:bg-blue-600 text-white border-none', [
              text('科技藍')
            ]),
            button(classes: 'btn w-full bg-amber-500 hover:bg-amber-600 text-white border-none', [
              text('工作琥珀')
            ]),
          ]
        ),

        // 按鈕尺寸卡片
        _buildCard(
          title: '按鈕尺寸',
          content: [
            button(classes: 'btn btn-primary btn-xs w-full mb-3', [text('超小')]),
            button(classes: 'btn btn-primary btn-sm w-full mb-3', [text('小')]),
            button(classes: 'btn btn-primary w-full mb-3', [text('普通')]),
            button(classes: 'btn btn-primary btn-lg w-full', [text('大')]),
          ]
        ),
      ]),
    ]);
  }

  /// 輔助方法：建立卡片
  Component _buildCard({required String title, required List<Component> content}) {
    return div(classes: 'bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300', [
      h3(classes: 'text-lg font-semibold mb-4', [text(title)]),
      div(classes: 'space-y-3', content),
    ]);
  }

  /// 卡片測試區域 - V0 風格
  Component _buildCardTestSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('卡片測試區域')
      ]),
      div(classes: 'grid md:grid-cols-3 gap-6', [

        // 基礎卡片
        div(classes: 'bg-white rounded-lg shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300', [
          div(classes: 'aspect-video bg-gradient-to-r from-amber-100 to-orange-100 rounded-t-lg flex items-center justify-center', [
            span(classes: 'text-4xl', [text('☕')])
          ]),
          div(classes: 'p-6', [
            h3(classes: 'text-xl font-bold mb-2', [text('基礎卡片')]),
            p(classes: 'text-gray-600 mb-4', [text('展示圖片和內容組合')]),
            button(classes: 'btn btn-primary w-full', [text('查看詳情')]),
          ]),
        ]),

        // 特色推薦卡片
        div(classes: 'relative bg-white rounded-lg shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300', [
          span(classes: 'absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded text-sm font-medium', [
            text('✨ 推薦')
          ]),
          div(classes: 'aspect-video bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg flex items-center justify-center', [
            span(classes: 'text-4xl', [text('🏢')])
          ]),
          div(classes: 'p-6', [
            h3(classes: 'text-xl font-bold mb-2', [text('特色推薦')]),
            div(classes: 'flex gap-2 flex-wrap mb-4', [
              span(classes: 'bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm', [text('Wi-Fi')]),
              span(classes: 'bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm', [text('安靜')]),
              span(classes: 'bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm', [text('插座')]),
            ]),
            button(classes: 'btn w-full bg-[#015899] hover:bg-[#014080] text-white border-none', [
              text('立即預約')
            ]),
          ]),
        ]),

        // HSN 風格卡片
        div(classes: 'bg-white rounded-lg shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 border-2 border-blue-100', [
          div(classes: 'p-6', [
            div(classes: 'text-sm text-blue-600 font-medium mb-2', [text('台北大安區')]),
            h3(classes: 'text-xl font-bold mb-2 text-[#015899]', [text('HSN 風格卡片')]),
            p(classes: 'text-gray-600 mb-4', [text('科技感懸停效果')]),
            div(classes: 'space-y-3', [
              div(classes: 'flex items-center gap-2', [
                span(classes: 'text-green-500', [text('📶')]),
                span(classes: 'text-sm', [text('Wi-Fi 優秀')]),
              ]),
              div(classes: 'flex items-center gap-2', [
                span(classes: 'text-blue-500', [text('⚡')]),
                span(classes: 'text-sm', [text('快速充電')]),
              ]),
            ]),
          ]),
        ]),
      ]),
    ]);
  }

  /// 遊牧指數視覺化區域 - 核心功能
  Component _buildNomadScorecardSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('遊牧指數視覺化')
      ]),
      div(classes: 'max-w-4xl mx-auto', [
        // 暫時使用佔位符，稍後實現完整的 NomadScorecard
        div(classes: 'bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-100 rounded-lg p-8 text-center', [
          h3(classes: 'text-2xl text-[#015899] mb-4', [text('遊牧指數')]),
          p(classes: 'text-gray-600 mb-6', [text('綜合工作環境評估')]),
          div(classes: 'text-6xl font-bold text-[#015899] mb-4', [text('8.5/10')]),
          p(classes: 'text-sm text-gray-500', [text('🔄 完整的圓形指示器即將實現...')]),
        ]),
      ]),
    ]);
  }

  /// 提示訊息區域 - V0 風格
  Component _buildAlertSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('提示訊息區域')
      ]),
      div(classes: 'max-w-2xl mx-auto space-y-4', [
        // 資訊提示
        div(classes: 'border-blue-200 bg-blue-50 border rounded-lg p-4', [
          div(classes: 'flex items-center gap-2', [
            span([text('ℹ️')]),
            span([text('這是一個資訊提示訊息')]),
          ]),
        ]),
        // 成功提示
        div(classes: 'border-green-200 bg-green-50 border rounded-lg p-4', [
          div(classes: 'flex items-center gap-2', [
            span([text('✅')]),
            span([text('DaisyUI 整合成功！')]),
          ]),
        ]),
        // 警告提示
        div(classes: 'border-yellow-200 bg-yellow-50 border rounded-lg p-4', [
          div(classes: 'flex items-center gap-2', [
            span([text('⚠️')]),
            span([text('這是一個警告訊息')]),
          ]),
        ]),
        // 錯誤提示
        div(classes: 'border-red-200 bg-red-50 border rounded-lg p-4', [
          div(classes: 'flex items-center gap-2', [
            span([text('❌')]),
            span([text('這是一個錯誤訊息')]),
          ]),
        ]),
      ]),
    ]);
  }

  /// 徽章測試區域 - V0 風格
  Component _buildBadgeSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('徽章測試區域')
      ]),
      div(classes: 'grid md:grid-cols-2 gap-6 max-w-2xl mx-auto', [

        // 基礎徽章卡片
        _buildCard(
          title: '基礎徽章',
          content: [
            div(classes: 'flex flex-wrap gap-2', [
              span(classes: 'bg-gray-900 text-white px-2 py-1 rounded text-sm', [text('預設')]),
              span(classes: 'bg-gray-900 text-white px-2 py-1 rounded text-sm', [text('主要')]),
              span(classes: 'bg-gray-600 text-white px-2 py-1 rounded text-sm', [text('次要')]),
              span(classes: 'bg-red-600 text-white px-2 py-1 rounded text-sm', [text('強調')]),
              span(classes: 'border border-gray-300 text-gray-700 px-2 py-1 rounded text-sm', [text('幽靈')]),
            ]),
          ]
        ),

        // 輪廓徽章卡片
        _buildCard(
          title: '輪廓徽章',
          content: [
            div(classes: 'flex flex-wrap gap-2', [
              span(classes: 'border border-blue-500 text-blue-500 px-2 py-1 rounded text-sm', [text('藍色')]),
              span(classes: 'border border-green-500 text-green-500 px-2 py-1 rounded text-sm', [text('綠色')]),
              span(classes: 'border border-yellow-500 text-yellow-500 px-2 py-1 rounded text-sm', [text('黃色')]),
              span(classes: 'border border-red-500 text-red-500 px-2 py-1 rounded text-sm', [text('紅色')]),
            ]),
          ]
        ),
      ]),
    ]);
  }

  /// 表單測試區域 - V0 風格
  Component _buildFormSection() {
    return section([
      h2(classes: 'text-3xl font-bold text-center mb-8 text-gray-800', [
        text('表單測試區域')
      ]),
      div(classes: 'max-w-md mx-auto', [
        _buildCard(
          title: '電子報訂閱測試',
          content: [
            form(classes: 'space-y-4', [
              // 電子郵件輸入
              div([
                input(
                  type: InputType.email,
                  classes: 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                  attributes: {'placeholder': '電子郵件地址'}
                ),
                p(classes: 'text-sm text-gray-500 mt-1', [
                  text('定期發送遊牧工作空間推薦')
                ]),
              ]),

              // 同意條款
              div(classes: 'flex items-center space-x-2', [
                input(
                  type: InputType.checkbox,
                  id: 'newsletter',
                  classes: 'w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'
                ),
                label(
                  classes: 'text-sm text-gray-700',
                  attributes: {'for': 'newsletter'},
                  [text('我同意接收電子報')]
                ),
              ]),

              // 按鈕組
              div(classes: 'flex gap-2', [
                button(
                  classes: 'flex-1 btn btn-outline bg-transparent',
                  [text('取消')]
                ),
                button(
                  classes: 'flex-1 btn bg-[#015899] hover:bg-[#014080] text-white border-none',
                  [text('訂閱電子報')]
                ),
              ]),
            ]),
          ]
        ),
      ]),
    ]);
  }
}
