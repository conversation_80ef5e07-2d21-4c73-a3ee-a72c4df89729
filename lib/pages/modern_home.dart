import 'package:jaspr/jaspr.dart';
import '../components/daisyui_hsn_bridge.dart';

/// 現代化首頁 - 使用 DaisyUI + HSN 橋接元件
@client
class ModernHome extends StatefulComponent {
  const ModernHome({super.key});

  @override
  State<ModernHome> createState() => ModernHomeState();
}

class ModernHomeState extends State<ModernHome> {
  String currentTheme = 'light';

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      print("Modern Home - Client");
    } else {
      print("Modern Home - Server");
    }
  }

  void toggleTheme() {
    setState(() {
      currentTheme = currentTheme == 'light' ? 'dark' : 'light';
    });
  }

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(
      classes: 'min-h-screen bg-base-100',
      attributes: {'data-theme': currentTheme},
      [
        // 現代化導航列
        ModernNavbar(
          brandText: '遊牧好點',
          brandIcon: '',
          navItems: [
            NavItem(text: '特色功能', href: '#features'),
            NavItem(text: '工作空間', href: '#workspaces'),
            NavItem(text: '關於我們', href: '/about'),
          ],
          actions: [
            ModernButton(
              text: '主題',
              variant: 'ghost',
              size: 'sm',
              onPressed: toggleTheme,
            ),
            ModernButton(
              text: 'UI 示例',
              variant: 'outline',
              size: 'sm',
              onPressed: () => print('Navigate to UI demo'),
            ),
          ],
        ),

        // Hero 區塊
        div(classes: 'hero min-h-screen hsn-gradient-primary', [
          div(classes: 'hero-content text-center text-white', [
            div(classes: 'max-w-4xl hsn-fade-in', [
              // 主標題
              h1(classes: 'text-6xl font-bold mb-6', [
                span(classes: 'hsn-text-gradient', [text('遊牧')]),
                text('好點'),
              ]),
              
              // 副標題
              p(classes: 'text-xl mb-8 max-w-2xl mx-auto opacity-90', [
                text('探索台灣最佳數位遊牧工作空間，享受現代化設計體驗！')
              ]),

              // 統計數據
              ModernStats(
                stats: [
                  StatItem(
                    title: '網路品質',
                    value: '高速',
                    description: '穩定連線保證',
                    icon: 'WiFi',
                    color: 'warning',
                  ),
                  StatItem(
                    title: '環境品質',
                    value: '舒適',
                    description: '專業工作環境',
                    icon: '舒適',
                    color: 'error',
                  ),
                  StatItem(
                    title: '服務品質',
                    value: '保證',
                    description: '精選認證空間',
                    icon: '認證',
                    color: 'success',
                  ),
                ],
              ),

              // 主要行動按鈕
              div(classes: 'flex flex-wrap gap-4 justify-center mt-8', [
                ModernButton(
                  text: '開始探索工作空間',
                  variant: 'accent',
                  size: 'lg',
                  onPressed: () => print('Start exploring'),
                ),
                ModernButton(
                  text: 'AI 智能推薦',
                  variant: 'secondary',
                  size: 'lg',
                  onPressed: () => print('AI recommendation'),
                ),
                ModernButton(
                  text: '探索特色空間',
                  variant: 'outline',
                  size: 'lg',
                  onPressed: () => print('Explore featured'),
                ),
              ]),
            ]),
          ]),
        ]),

        // 工作空間展示區域
        div(id: 'workspaces', classes: 'py-20 bg-base-200', [
          div(classes: 'container mx-auto px-8', [
            // 區塊標題
            div(classes: 'text-center mb-16 hsn-fade-in', [
              h2(classes: 'text-4xl font-bold text-base-content mb-4', [
                text('精選工作空間')
              ]),
              p(classes: 'text-lg text-base-content/70 max-w-2xl mx-auto', [
                text('我們精心挑選台灣各地最適合數位遊牧的工作空間')
              ]),
            ]),
            
            // 工作空間卡片網格
            div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8', [
              // 卡片 1 - 星巴克敦南
              ModernCard(
                title: '星巴克 敦南門市',
                subtitle: '寬敞舒適的工作環境，提供高速 Wi-Fi 和充足插座',
                imageUrl: 'COFFEE',
                variant: 'default',
                children: [
                  div(classes: 'flex items-center mb-2', [
                    ModernBadge(text: '台北大安區', variant: 'primary'),
                  ]),
                  div(classes: 'flex items-center gap-4 mt-4', [
                    ModernBadge(text: 'Wi-Fi 優秀', variant: 'success'),
                    ModernBadge(text: '咖啡供應', variant: 'secondary'),
                  ]),
                ],
                actions: [
                  ModernButton(
                    text: '查看詳情',
                    variant: 'primary',
                    size: 'sm',
                    onPressed: () => print('View details'),
                  ),
                ],
              ),

              // 卡片 2 - 微風南山（特色推薦）
              ModernCard(
                title: '微風南山 共享空間',
                subtitle: '現代化共享辦公空間，完美適合數位遊牧工作',
                imageUrl: 'OFFICE',
                variant: 'featured',
                badge: '推薦',
                children: [
                  div(classes: 'flex items-center mb-2', [
                    ModernBadge(text: '台北信義區', variant: 'accent'),
                  ]),
                  div(classes: 'flex items-center gap-4 mt-4', [
                    ModernBadge(text: 'Wi-Fi 優秀', variant: 'accent'),
                    ModernBadge(text: '快速充電', variant: 'warning'),
                  ]),
                ],
                actions: [
                  ModernButton(
                    text: '立即預約',
                    variant: 'accent',
                    size: 'sm',
                    onPressed: () => print('Book now'),
                  ),
                ],
              ),

              // 卡片 3 - 誠品書店
              ModernCard(
                title: '誠品書店 松菸店',
                subtitle: '書香環繞的工作環境，安靜舒適適合專注工作',
                imageUrl: 'BOOKS',
                variant: 'default',
                children: [
                  div(classes: 'flex items-center mb-2', [
                    ModernBadge(text: '台北中山區', variant: 'primary'),
                  ]),
                  div(classes: 'flex items-center gap-4 mt-4', [
                    ModernBadge(text: 'Wi-Fi 良好', variant: 'info'),
                    ModernBadge(text: '閱讀氛圍', variant: 'neutral'),
                  ]),
                ],
                actions: [
                  ModernButton(
                    text: '查看詳情',
                    variant: 'primary',
                    size: 'sm',
                    onPressed: () => print('View details'),
                  ),
                ],
              ),
            ])
          ])
        ]),

        // 現代化 Footer
        footer(classes: 'footer footer-center p-10 bg-base-200 text-base-content rounded', [
          div(classes: 'grid grid-flow-col gap-4', [
            a(href: '/about', classes: 'link link-hover', [text('關於我們')]),
            a(href: '#', classes: 'link link-hover', [text('聯絡方式')]),
            a(href: '#', classes: 'link link-hover', [text('使用條款')]),
            a(href: '#', classes: 'link link-hover', [text('隱私政策')]),
          ]),
          div([
            div(classes: 'grid grid-flow-col gap-4', [
              ModernButton(
                text: '♡',
                variant: 'ghost',
                size: 'sm',
                onPressed: () => print('Like'),
              ),
              ModernButton(
                text: '★',
                variant: 'ghost',
                size: 'sm',
                onPressed: () => print('Star'),
              ),
              ModernButton(
                text: 'UI',
                variant: 'ghost',
                size: 'sm',
                onPressed: () => print('Design'),
              ),
            ])
          ]),
          div([
            p(classes: 'flex items-center gap-2', [
              text('Powered by '),
              span(classes: 'font-bold text-primary', [text('Jaspr')]),
              text(' + '),
              span(classes: 'font-bold text-secondary', [text('DaisyUI')]),
            ]),
            p(classes: 'text-sm opacity-70', [text('© 2024 遊牧好點 - 探索最佳工作空間')])
          ])
        ])
      ]
    );
  }
}
