import 'package:jaspr/jaspr.dart';
import 'package:jaspr_router/jaspr_router.dart';

import 'pages/about.dart';
import 'pages/home.dart';
import 'pages/modern_home.dart';
import 'pages/daisyui_test.dart';
import 'pages/daisyui_demo.dart';

// The main component of your application.
//
// By using multi-page routing, this component will only be built on the server during pre-rendering and
// **not** executed on the client. Instead only the nested [Home] and [About] components will be mounted on the client.
class App extends StatelessComponent {
  const App({super.key});

  @override
  Iterable<Component> build(BuildContext context) sync* {
    // This method is rerun every time the component is rebuilt.
    //
    // Each build method can return multiple child components as an [Iterable]. The recommended approach
    // is using the [sync* / yield] syntax for a streamlined control flow, but its also possible to simply
    // create and return a [List] here.

    // Renders a <div class="main"> html element with children.
    // 添加 DaisyUI 必需的 data-theme 屬性
    yield div(
      classes: 'main',
      attributes: {'data-theme': 'light'},
      [
      // 暫時移除 Header 讓內容能夠正確居中
      // const Header(),
      Router(routes: [
        Route(
            path: '/',
            title: 'Home',
            builder: (context, state) => const Home()),
        Route(
            path: '/about',
            title: 'About',
            builder: (context, state) => const About()),
        Route(
            path: '/daisyui-test',
            title: 'DaisyUI Test',
            builder: (context, state) => DaisyUITestPage()),
        Route(
            path: '/daisyui-demo',
            title: 'DaisyUI Demo',
            builder: (context, state) => const DaisyUIDemoPage()),
        Route(
            path: '/modern',
            title: 'Modern Home',
            builder: (context, state) => const ModernHome()),
      ]),
    ]);
  }

  // Defines the css styles for elements of this component.
  //
  // By using the @css annotation, these will be rendered automatically to css inside the <head> of your page.
  // Must be a variable or getter of type [List<StyleRule>].
  @css
  static List<StyleRule> get styles => [
        css('.main', [
          // The '&' refers to the parent selector of a nested style rules.
          css('&').styles(
            display: Display.flex,
            height: 100.vh,
            flexDirection: FlexDirection.column,
            flexWrap: FlexWrap.wrap,
          ),
          css('section').styles(
            display: Display.flex,
            flexDirection: FlexDirection.column,
            justifyContent: JustifyContent.center,
            alignItems: AlignItems.center,
            flex: Flex(grow: 1),
          ),
        ]),
      ];
}
