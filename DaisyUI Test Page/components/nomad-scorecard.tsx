"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useEffect, useState } from "react"

interface ScoreIndicatorProps {
  score: number
  maxScore?: number
  size?: "sm" | "lg"
  className?: string
}

function ScoreIndicator({ score, maxScore = 10, size = "lg", className = "" }: ScoreIndicatorProps) {
  const [animatedScore, setAnimatedScore] = useState(0)
  const percentage = (score / maxScore) * 100
  const circumference = size === "lg" ? 2 * Math.PI * 45 : 2 * Math.PI * 30
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  useEffect(() => {
    const timer = setTimeout(() => setAnimatedScore(score), 300)
    return () => clearTimeout(timer)
  }, [score])

  const sizeClasses = size === "lg" ? "w-24 h-24" : "w-16 h-16"
  const radius = size === "lg" ? 45 : 30
  const textSize = size === "lg" ? "text-xl" : "text-sm"

  return (
    <div className={`relative ${sizeClasses} ${className}`}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth="4"
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth="4"
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-500 transition-all duration-1000 ease-out"
          strokeLinecap="round"
        />
      </svg>
      <div className={`absolute inset-0 flex items-center justify-center ${textSize} font-bold text-gray-800`}>
        {animatedScore.toFixed(1)}
      </div>
    </div>
  )
}

interface MetricCardProps {
  icon: string
  title: string
  score: number
  details: string
  description: string
  level: "EXCELLENT" | "GREAT" | "GOOD" | "FAIR"
}

function MetricCard({ icon, title, score, details, description, level }: MetricCardProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => setAnimatedProgress(score * 10), 500)
    return () => clearTimeout(timer)
  }, [score])

  const getScoreColor = (score: number) => {
    if (score >= 9) return "text-green-500"
    if (score >= 8) return "text-blue-500"
    if (score >= 7) return "text-yellow-500"
    return "text-orange-500"
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case "EXCELLENT":
        return "bg-green-500"
      case "GREAT":
        return "bg-blue-500"
      case "GOOD":
        return "bg-yellow-500"
      default:
        return "bg-orange-500"
    }
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl">{icon}</span>
            <CardTitle className="text-lg">{title}</CardTitle>
          </div>
          <Badge className={`${getLevelColor(level)} text-white text-xs`}>{level}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className={`text-2xl font-bold ${getScoreColor(score)}`}>{score.toFixed(1)}</span>
          <span className="text-sm text-gray-600">{details}</span>
        </div>
        <Progress value={animatedProgress} className="h-2" />
        <p className="text-sm text-gray-600">{description}</p>
      </CardContent>
    </Card>
  )
}

export function NomadScorecard() {
  return (
    <div className="space-y-6">
      {/* 完整版 Scorecard */}
      <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-100">
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-2xl text-[#015899] mb-2">遊牧指數</CardTitle>
          <p className="text-gray-600 mb-4">綜合工作環境評估</p>
          <div className="flex justify-center">
            <ScoreIndicator score={8.5} />
          </div>
          <div className="text-3xl font-bold text-[#015899] mt-2">8.5/10</div>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <MetricCard
              icon="⚡"
              title="Wi-Fi 指標"
              score={9.2}
              details="85Mbps"
              description="光纖網路，實測穩定"
              level="EXCELLENT"
            />
            <MetricCard
              icon="🔌"
              title="電源指標"
              score={8.5}
              details="豐富"
              description="每桌至少一個插座"
              level="GREAT"
            />
            <MetricCard
              icon="🤫"
              title="安靜度指標"
              score={7.8}
              details="適度背景音"
              description="適合專注工作"
              level="GOOD"
            />
          </div>
        </CardContent>
      </Card>

      {/* 簡化版 Scorecard */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="font-semibold text-gray-800">遊牧指數</span>
              <div className="flex items-center gap-1">
                <ScoreIndicator score={8.5} size="sm" />
                <span className="font-bold text-[#015899]">8.5/10</span>
              </div>
            </div>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <span>📶</span>
                <span className="font-medium text-green-600">9.2</span>
              </div>
              <div className="flex items-center gap-1">
                <span>🔌</span>
                <span className="font-medium text-blue-600">8.5</span>
              </div>
              <div className="flex items-center gap-1">
                <span>🤫</span>
                <span className="font-medium text-yellow-600">7.8</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
