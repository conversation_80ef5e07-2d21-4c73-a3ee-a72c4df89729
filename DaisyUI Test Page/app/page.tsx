import { NomadScorecard } from "@/components/nomad-scorecard"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function DaisyUITestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-20 text-center">
          <h1 className="text-5xl font-bold mb-4 text-balance">DaisyUI + HSN</h1>
          <p className="text-xl mb-8 text-blue-100 text-pretty">測試頁面 - 展示所有組件效果</p>
          <button className="btn btn-primary btn-lg gap-2 shadow-lg hover:shadow-xl transition-all duration-300">
            開始測試 🚀
          </button>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12 space-y-16">
        {/* 按鈕測試區域 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">按鈕測試區域</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {/* DaisyUI 基礎按鈕卡片 */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle>DaisyUI 按鈕</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="btn btn-primary w-full">Primary</button>
                <button className="btn btn-secondary w-full">Secondary</button>
                <button className="btn btn-accent w-full">Accent</button>
                <button className="btn btn-success w-full">Success</button>
                <button className="btn btn-warning w-full">Warning</button>
                <button className="btn btn-error w-full">Error</button>
              </CardContent>
            </Card>

            {/* HSN 風格按鈕卡片 */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle>HSN 風格按鈕</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="btn w-full bg-[#015899] hover:bg-[#014080] text-white border-none">遊牧主色</button>
                <button className="btn w-full bg-blue-500 hover:bg-blue-600 text-white border-none">科技藍</button>
                <button className="btn w-full bg-amber-500 hover:bg-amber-600 text-white border-none">工作琥珀</button>
              </CardContent>
            </Card>

            {/* 按鈕尺寸卡片 */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle>按鈕尺寸</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button className="btn btn-primary btn-xs w-full">超小</button>
                <button className="btn btn-primary btn-sm w-full">小</button>
                <button className="btn btn-primary w-full">普通</button>
                <button className="btn btn-primary btn-lg w-full">大</button>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 卡片測試區域 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">卡片測試區域</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {/* 基礎卡片 */}
            <Card className="hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
              <div className="aspect-video bg-gradient-to-r from-amber-100 to-orange-100 rounded-t-lg flex items-center justify-center">
                <span className="text-4xl">☕</span>
              </div>
              <CardHeader>
                <CardTitle>基礎卡片</CardTitle>
                <CardDescription>展示圖片和內容組合</CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">查看詳情</Button>
              </CardContent>
            </Card>

            {/* 特色推薦卡片 */}
            <Card className="relative hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
              <Badge className="absolute top-4 right-4 bg-yellow-500 text-white">✨ 推薦</Badge>
              <div className="aspect-video bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg flex items-center justify-center">
                <span className="text-4xl">🏢</span>
              </div>
              <CardHeader>
                <CardTitle>特色推薦</CardTitle>
                <div className="flex gap-2 flex-wrap">
                  <Badge variant="secondary">Wi-Fi</Badge>
                  <Badge variant="secondary">安靜</Badge>
                  <Badge variant="secondary">插座</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-[#015899] hover:bg-[#014080]">立即預約</Button>
              </CardContent>
            </Card>

            {/* HSN 風格卡片 */}
            <Card className="hover:shadow-xl hover:-translate-y-1 transition-all duration-300 border-2 border-blue-100">
              <CardHeader>
                <div className="text-sm text-blue-600 font-medium">台北大安區</div>
                <CardTitle className="text-[#015899]">HSN 風格卡片</CardTitle>
                <CardDescription>科技感懸停效果</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <span className="text-green-500">📶</span>
                  <span className="text-sm">Wi-Fi 優秀</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-blue-500">⚡</span>
                  <span className="text-sm">快速充電</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 核心功能：遊牧指數視覺化 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">遊牧指數視覺化</h2>
          <div className="max-w-4xl mx-auto">
            <NomadScorecard />
          </div>
        </section>

        {/* 提示訊息區域 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">提示訊息區域</h2>
          <div className="max-w-2xl mx-auto space-y-4">
            <Alert className="border-blue-200 bg-blue-50">
              <AlertDescription className="flex items-center gap-2">
                <span>ℹ️</span>
                這是一個資訊提示訊息
              </AlertDescription>
            </Alert>
            <Alert className="border-green-200 bg-green-50">
              <AlertDescription className="flex items-center gap-2">
                <span>✅</span>
                DaisyUI 整合成功！
              </AlertDescription>
            </Alert>
            <Alert className="border-yellow-200 bg-yellow-50">
              <AlertDescription className="flex items-center gap-2">
                <span>⚠️</span>
                這是一個警告訊息
              </AlertDescription>
            </Alert>
            <Alert className="border-red-200 bg-red-50">
              <AlertDescription className="flex items-center gap-2">
                <span>❌</span>
                這是一個錯誤訊息
              </AlertDescription>
            </Alert>
          </div>
        </section>

        {/* 徽章測試區域 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">徽章測試區域</h2>
          <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>基礎徽章</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-wrap gap-2">
                <Badge>預設</Badge>
                <Badge variant="default">主要</Badge>
                <Badge variant="secondary">次要</Badge>
                <Badge variant="destructive">強調</Badge>
                <Badge variant="outline">幽靈</Badge>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>輪廓徽章</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-wrap gap-2">
                <Badge variant="outline" className="border-blue-500 text-blue-500">
                  藍色
                </Badge>
                <Badge variant="outline" className="border-green-500 text-green-500">
                  綠色
                </Badge>
                <Badge variant="outline" className="border-yellow-500 text-yellow-500">
                  黃色
                </Badge>
                <Badge variant="outline" className="border-red-500 text-red-500">
                  紅色
                </Badge>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 表單測試區域 */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">表單測試區域</h2>
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>電子報訂閱測試</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Input type="email" placeholder="電子郵件地址" className="w-full" />
                <p className="text-sm text-gray-500 mt-1">定期發送遊牧工作空間推薦</p>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="newsletter" />
                <label htmlFor="newsletter" className="text-sm">
                  我同意接收電子報
                </label>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" className="flex-1 bg-transparent">
                  取消
                </Button>
                <Button className="flex-1 bg-[#015899] hover:bg-[#014080]">訂閱電子報</Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
